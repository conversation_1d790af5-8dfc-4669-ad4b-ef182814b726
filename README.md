# Spy-vanilla for TurtleWoW

A World of Warcraft addon that **detects and alerts you to the presence of nearby enemy players**. This version is specifically ported and optimized for vanilla WoW clients, particularly TurtleWoW.

## About

Spy is a PvP awareness tool that helps players detect enemy players in their vicinity. Originally ported from: https://github.com/laytya/Spy-vanilla

## Features

- Detects nearby enemy players
- Audio and visual alerts for different threat levels
- Maintains KOS (Kill on Sight) lists
- Player statistics and encounter tracking
- Customizable GUI and alert settings
- Supports stealth detection and PvP-enabled player alerts

## Fixed Issues

✅ **ChatThrottleLib Compatibility**: Fixed the AceComm-3.0 library errors that occurred due to version incompatibility between modern WoW message length restrictions and vanilla WoW clients. The fix uses proper version detection instead of disabling safety checks.

## Installation

1. Download or clone this repository
2. Place the `Spy-twow` folder in your `Interface/AddOns/` directory
3. Restart WoW or reload your UI (`/reload`)
4. The addon should appear in your addon list

## Compatibility

- **TurtleWoW**: Fully supported
- **Vanilla WoW 1.12**: Should work on other vanilla servers
- **Interface Version**: 11200
