--local BS = AceLibrary("Babble-Spell-2.3")
local B = LibStub("LibBabble-Spell-3.0")
local BS = B:GetLookupTable()
local uc = LibStub:GetLibrary("UnitCasting-1.1")

---------------------------------------------------------
-- SPELLS:
---------------------------------------------------------

uc.RegisterTable("SpellcastsToTrack", {
    [BS["Acid Spit"]]                        = { casttime = 3, },
    [BS["Aimed Shot"]]                       = { casttime = 2.5, immune = true, class = 'HUNTER', type = 'dmg',
        school = 'physical', },
    [BS["Ancestral Spirit"]]                 = { casttime = 10, },
    [BS["Arcane Bolt"]]                      = { casttime = 1, },
    [BS["Arcane Bomb"]]                      = { casttime = 1, },
    [BS["Astral Recall"]]                    = { casttime = 10, },
    [BS["Banish"]]                           = { casttime = 1.5, },
    [BS["Banshee Curse"]]                    = { casttime = 2, },
    [BS["Banshee Wail"]]                     = { casttime = 1.5, },
    [BS["Bellowing Roar"]]                   = { casttime = 1.75, },
    [BS["Big Bronze Bomb"]]                  = { casttime = 1, },
    [BS["Big Iron Bomb"]]                    = { casttime = 1, },
    [BS["Black Sludge"]]                     = { casttime = 3, },
    [BS["Bomb"]]                             = { casttime = 2, },
    [BS["Boulder"]]                          = { casttime = 2, },
    [BS["Breath"]]                           = { casttime = 8, },
    [BS["Chain Heal"]]                       = { casttime = 2.5, },
    [BS["Chain Lightning"]]                  = { casttime = 1.5, class = 'SHAMAN', type = 'dmg', school = 'nature', },
    [BS["Chained Bolt"]]                     = { casttime = 1.8, },
    [BS["Chains of Ice"]]                    = { casttime = 1.5, },
    [BS["Clone"]]                            = { casttime = 2.5, },
    [BS["Conjure Food"]]                     = { casttime = 3, class = 'MAGE', },
    [BS["Conjure Mana Agate"]]               = { casttime = 3, class = 'MAGE', },
    [BS["Conjure Mana Citrine"]]             = { casttime = 3, class = 'MAGE', },
    [BS["Conjure Mana Jade"]]                = { casttime = 3, class = 'MAGE', },
    [BS["Conjure Mana Ruby"]]                = { casttime = 3, class = 'MAGE', },
    [BS["Conjure Water"]]                    = { casttime = 3, class = 'MAGE', },
    [BS["Create Firestone"]]                 = { casttime = 3, },
    [BS["Create Healthstone (Major)"]]       = { casttime = 3, },
    [BS["Create Soulstone (Major)"]]         = { casttime = 3, },
    [BS["Create Spellstone"]]                = { casttime = 5, },
    [BS["Creeping Mold"]]                    = { casttime = 3, },
    [BS["Cripple"]]                          = { casttime = 3, },
    [BS["Crippling Poison"]]                 = { casttime = 3, },
    [BS["Crystal Flash"]]                    = { casttime = 2, immune = true, },
    [BS["Crystal Gaze"]]                     = { casttime = 2, immune = true, },
    [BS["Crystalline Slumber"]]              = { casttime = 2, immune = true, },
    [BS["Cultivate Packet of Seeds"]]        = { casttime = 10, },
    [BS["Curse of Mending"]]                 = { casttime = 1, },
    [BS["Curse of the Deadwood"]]            = { casttime = 2, },
    [BS["Curse of the Fallen Magram"]]       = { casttime = 2, },
    [BS["Dark Iron Bomb"]]                   = { casttime = 1, },
    [BS["Dark Mending"]]                     = { casttime = 2, },
    [BS["Dark Sludge"]]                      = { casttime = 5, },
    [BS["Deadly Poison II"]]                 = { casttime = 3, },
    [BS["Deadly Poison III"]]                = { casttime = 3, },
    [BS["Deadly Poison IV"]]                 = { casttime = 3, },
    [BS["Deadly Poison V"]]                  = { casttime = 3, },
    [BS["Deadly Poison"]]                    = { casttime = 3, },
    [BS["Decayed Strength"]]                 = { casttime = 2, },
    [BS["Dimensional Ripper - Everlook"]]    = { casttime = 10, },
    [BS["Dire Growl"]]                       = { casttime = 1, immune = true, },
    [BS["Disarm Trap"]]                      = { casttime = 5, },
    [BS["Diseased Slime"]]                   = { casttime = 2, immune = true, },
    [BS["Disenchant"]]                       = { casttime = 3, },
    [BS["Dismiss Pet"]]                      = { casttime = 5, },
    [BS["Dominate Mind"]]                    = { casttime = 2, },
    [BS["Drink Minor Potion"]]               = { casttime = 3, },
    [BS["Dynamite"]]                         = { casttime = 1, },
    [BS["Earthgrab Totem"]]                  = { casttime = 0.5, },
    [BS["Elemental Fire"]]                   = { casttime = 0.5, },
    [BS["Enslave Demon"]]                    = { casttime = 3, },
    [BS["Entangling Roots"]]                 = { casttime = 1.5, class = 'DRUID', type = 'dmg', school = 'nature', },
    [BS["Enveloping Winds"]]                 = { casttime = 2, },
    [BS["Escape Artist"]]                    = { casttime = 0.5, },
    [BS["Explode"]]                          = { casttime = 6, },
    [BS["Exploding Shot"]]                   = { casttime = 1, },
    [BS["Eye of Kilrogg"]]                   = { casttime = 5, },
    [BS["Eyes of the Beast"]]                = { casttime = 2, },
    [BS["Far Sight"]]                        = { casttime = 2, },
    [BS["Fear"]]                             = { casttime = 1.5, class = 'WARLOCK', type = 'dmg', school = 'shadow', },
    [BS["Fevered Fatigue"]]                  = { casttime = 3, },
    [BS["Fire Shield II"]]                   = { casttime = 1, },
    [BS["Fireball"]]                         = { casttime = 2.5, class = 'MAGE', type = 'dmg', school = 'fire', },
    [BS["Firebolt"]]                         = { casttime = 2, class = 'WARLOCK', type = 'dmg', school = 'fire', },
    [BS["Flame Buffet"]]                     = { casttime = 6, },
    [BS["Flame Spike"]]                      = { casttime = 3, },
    [BS["Flamespit"]]                        = { casttime = 3, },
    [BS["Flamestrike"]]                      = { casttime = 3, class = 'MAGE', },
    [BS["Flash Heal"]]                       = { casttime = 1.5, },
    [BS["Flash of Light"]]                   = { casttime = 1.5, },
    [BS["Freeze Solid"]]                     = { casttime = 2.5, },
    [BS["Frostbolt"]]                        = { casttime = 2.5, class = 'MAGE', type = 'dmg', school = 'frost', },
    [BS["Furbolg Form"]]                     = { casttime = 2, immune = true, },
    [BS["Ghost Wolf"]]                       = { casttime = 3, },
    [BS["Greater Heal"]]                     = { casttime = 2.5, },
    [BS["Hammer of Wrath"]]                  = { casttime = 1, },
    [BS["Heal"]]                             = { casttime = 2.5, },
    [BS["Healing Touch"]]                    = { casttime = 3, class = 'DRUID', },
    [BS["Healing Ward"]]                     = { casttime = 2, },
    [BS["Healing Wave"]]                     = { casttime = 2.5, },
    [BS["Hearthstone"]]                      = { casttime = 10, },
    [BS["Hibernate"]]                        = { casttime = 1.5, class = 'DRUID', type = 'dmg', school = 'nature', },
    [BS["Hi-Explosive Bomb"]]                = { casttime = 1, },
    [BS["Holy Fire"]]                        = { casttime = 3.5, class = 'PRIEST', type = 'dmg', school = 'holy', },
    [BS["Holy Light"]]                       = { casttime = 2.5, },
    [BS["Holy Smite"]]                       = { casttime = 2.5, },
    [BS["Holy Wrath"]]                       = { casttime = 2, },
    [BS["Howl of Terror"]]                   = { casttime = 2, class = 'WARLOCK', type = 'dmg', school = 'shadow', },
    [BS["Icicle"]]                           = { casttime = 1.5, },
    [BS["Immolate"]]                         = { casttime = 1.5, class = 'WARLOCK', type = 'dmg', school = 'fire', },
    [BS["Inferno"]]                          = { casttime = 2, },
    [BS["Ink Spray"]]                        = { casttime = 1, immune = true, },
    [BS["Instant Poison II"]]                = { casttime = 3, },
    [BS["Instant Poison III"]]               = { casttime = 3, },
    [BS["Instant Poison IV"]]                = { casttime = 3, },
    [BS["Instant Poison V"]]                 = { casttime = 3, },
    [BS["Instant Poison VI"]]                = { casttime = 3, },
    [BS["Instant Poison"]]                   = { casttime = 3, },
    [BS["Iron Grenade"]]                     = { casttime = 1, },
    [BS["Large Copper Bomb"]]                = { casttime = 1, },
    [BS["Lesser Heal"]]                      = { casttime = 1.5, },
    [BS["Lesser Healing Wave"]]              = { casttime = 1.5, },
    [BS["Lightning Blast"]]                  = { casttime = 3.2, },
    [BS["Lightning Bolt"]]                   = { casttime = 2.5, class = 'SHAMAN', type = 'dmg', school = 'nature', },
    [BS["Lizard Bolt"]]                      = { casttime = 2, },
    [BS["Magic Dust"]]                       = { casttime = 1.5, },
    [BS["Magma Blast"]]                      = { casttime = 1, },
    [BS["Mana Burn"]]                        = { casttime = 3, class = 'PRIEST', type = 'dmg', school = 'shadow', },
    [BS["Mind Blast"]]                       = { casttime = 1.5, class = 'PRIEST', type = 'dmg', school = 'shadow', },
    [BS["Mind Control"]]                     = { casttime = 3, class = 'PRIEST', type = 'dmg', school = 'shadow', },
    [BS["Mind-numbing Poison II"]]           = { casttime = 3, },
    [BS["Mind-numbing Poison III"]]          = { casttime = 3, },
    [BS["Mind-numbing Poison"]]              = { casttime = 3, },
    [BS["Mithril Frag Bomb"]]                = { casttime = 1, },
    [BS["Multi-Shot"]]                       = { casttime = 0.5, immune = true, class = 'HUNTER', type = 'dmg',
        school = 'physical', },
    [BS["Pick Lock"]]                        = { casttime = 5, },
    [BS["Plague Cloud"]]                     = { casttime = 2, },
    [BS["Plague Mind"]]                      = { casttime = 4, },
    [BS["Poisoned Shot"]]                    = { casttime = 2, },
    [BS["Polymorph"]]                        = { casttime = 1.5, class = 'MAGE', type = 'dmg', school = 'arcane', },
    [BS["Polymorph: Pig"]]                   = { casttime = 1.5, class = 'MAGE', type = 'dmg', school = 'arcane', },
    [BS["Polymorph: Turtle"]]                = { casttime = 1.5, class = 'MAGE', type = 'dmg', school = 'arcane', },
    [BS["Portal: Darnassus"]]                = { casttime = 10, },
    [BS["Portal: Ironforge"]]                = { casttime = 10, },
    [BS["Portal: Orgrimmar"]]                = { casttime = 10, },
    [BS["Portal: Stormwind"]]                = { casttime = 10, },
    [BS["Portal: Thunder Bluff"]]            = { casttime = 10, },
    [BS["Portal: Undercity"]]                = { casttime = 10, },
    [BS["Prayer of Healing"]]                = { casttime = 3, },
    [BS["Pyroblast"]]                        = { casttime = 6, class = 'MAGE', type = 'dmg', school = 'fire', },
    [BS["Quick Flame Ward"]]                 = { casttime = 1.5, },
    [BS["Rebirth"]]                          = { casttime = 2, class = 'DRUID', },
    [BS["Reckless Charge"]]                  = { casttime = 0.1, },
    [BS["Redemption"]]                       = { casttime = 10, },
    [BS["Regrowth"]]                         = { casttime = 2, class = 'DRUID', },
    [BS["Resurrection"]]                     = { casttime = 10, },
    [BS["Revive Pet"]]                       = { casttime = 10, },
    [BS["Ritual of Doom"]]                   = { casttime = 10, },
    [BS["Ritual of Summoning"]]              = { casttime = 5, },
    [BS["Rough Copper Bomb"]]                = { casttime = 1, },
    [BS["Scare Beast"]]                      = { casttime = 1.5, class = 'HUNTER', type = 'dmg', school = 'physical', },
    [BS["Scorch"]]                           = { casttime = 1.5, class = 'MAGE', type = 'dmg', school = 'fire', },
    [BS["Searing Pain"]]                     = { casttime = 1.5, class = 'WARLOCK', type = 'dmg', school = 'fire', },
    [BS["Seduction"]]                        = { casttime = 1.5, class = 'WARLOCK', type = 'dmg', school = 'shadow', },
    [BS["Shackle Undead"]]                   = { casttime = 1.5, },
    [BS["Shadow Bolt"]]                      = { casttime = 2.5, class = 'WARLOCK', type = 'dmg', school = 'shadow', },
    [BS["Shadow Flame"]]                     = { casttime = 2, },
    [BS["Shrink"]]                           = { casttime = 3, },
    [BS["Silithid Pox"]]                     = { casttime = 2, },
    [BS["Slam"]]                             = { casttime = 1.5, immune = true, class = 'WARRRIOR', type = 'dmg',
        school = 'physical', },
    [BS["Sleep"]]                            = { casttime = 1.5, },
    [BS["Slowing Poison"]]                   = { casttime = 1, },
    [BS["Small Bronze Bomb"]]                = { casttime = 1, },
    [BS["Smite"]]                            = { casttime = 2.5, class = 'PRIEST', type = 'dmg', school = 'holy', },
    [BS["Soothe Animal"]]                    = { casttime = 1.5, class = 'DRUID', },
    [BS["Soul Fire"]]                        = { casttime = 4, class = 'WARLOCK', type = 'dmg', school = 'fire', },
    [BS["Soulstone Resurrection"]]           = { casttime = 3, },
    [BS["Starfire"]]                         = { casttime = 3, class = 'DRUID', type = 'dmg', school = 'nature', },
    [BS["Summon Charger"]]                   = { casttime = 3, },
    [BS["Summon Dreadsteed"]]                = { casttime = 3, },
    [BS["Summon Felhunter"]]                 = { casttime = 10, },
    [BS["Summon Felsteed"]]                  = { casttime = 3, },
    [BS["Summon Imp"]]                       = { casttime = 10, },
    [BS["Summon Ragnaros"]]                  = { casttime = 10, },
    [BS["Summon Succubus"]]                  = { casttime = 10, },
    [BS["Summon Voidwalker"]]                = { casttime = 10, },
    [BS["Summon Warhorse"]]                  = { casttime = 3, },
    [BS["Teleport: Darnassus"]]              = { casttime = 10, },
    [BS["Teleport: Ironforge"]]              = { casttime = 10, },
    [BS["Teleport: Moonglade"]]              = { casttime = 10, class = 'DRUID', },
    [BS["Teleport: Orgrimmar"]]              = { casttime = 10, },
    [BS["Teleport: Stormwind"]]              = { casttime = 10, },
    [BS["Teleport: Thunder Bluff"]]          = { casttime = 10, },
    [BS["Teleport: Undercity"]]              = { casttime = 10, },
    [BS["Thorium Grenade"]]                  = { casttime = 1, },
    [BS["Toxic Spit"]]                       = { casttime = 2.5, },
    [BS["Trelane's Freezing Touch"]]         = { casttime = 3, },
    [BS["Turn Undead"]]                      = { casttime = 1.5, },
    [BS["Ultrasafe Transporter: Gadgetzan"]] = { casttime = 10, },
    [BS["Venom Spit"]]                       = { casttime = 2.5, },
    [BS["Wandering Plague"]]                 = { casttime = 2, },
    [BS["War Stomp"]]                        = { casttime = 0.5, },
    [BS["Weak Frostbolt"]]                   = { casttime = 2.2, },
    [BS["Wing Buffet"]]                      = { casttime = 1, },
    [BS["Wither Touch"]]                     = { casttime = 2, },
    [BS["Wrath"]]                            = { casttime = 1.5, class = 'DRUID', type = 'dmg', school = 'nature', },
})

uc.RegisterTable("ChanneledHealsSpellcastsToTrack", {
    [BS["Health Funnel"]] = { casttime = 10, },
    [BS["Tranquility"]]   = { casttime = 10, },
    [BS["Mend Pet"]]      = { casttime = 5, },
    [BS["First Aid"]]     = { casttime = 7, },
})

uc.RegisterTable("ChanneledSpellcastsToTrack", {
    --MISC
    [BS["Cannibalize"]]       = { casttime = 10, },
    [BS["Gnomish Death Ray"]] = { casttime = 4, },
    -- DRUID
    [BS["Hurricane"]]         = { casttime = 9.5, },
    -- HUNTER
    [BS["Eagle Eye"]]         = { casttime = 60, },
    [BS["Eyes of the Beast"]] = { casttime = 60, },
    -- MAGE
    [BS["Arcane Missile"]]    = { casttime = 2.5, },
    [BS["Arcane Missiles"]]   = { casttime = 4.5, },
    [BS["Blizzard"]]          = { casttime = 7.5, },
    [BS["Evocation"]]         = { casttime = 8, },
    -- PRIEST
    [BS["Mind Flay"]]         = { casttime = 3, },
    [BS["Mind Vision"]]       = { casttime = 30, },
    -- WARLOCK
    [BS["Drain Life"]]        = { casttime = 4.5, },
    [BS["Drain Mana"]]        = { casttime = 4.5, },
    [BS["Drain Soul"]]        = { casttime = 14.5, },
    [BS["Rain of Fire"]]      = { casttime = 7.5, },
})

uc.RegisterTable("InstantSpellcastsToTrack", {
    [BS['Shoot']]            = true,
    -- DRUID
    [BS['Moonfire']]         = true,
    -- MAGE
    [BS['Arcane Explosion']] = true,
    [BS['Cone of Cold']]     = true,
    [BS['Fire Blast']]       = true,
    [BS['Frost Nova']]       = true,
    -- PALADIN
    [BS['Holy Shock']]       = true,
    -- PRIEST
    [BS['Holy Nova']]        = true,
    -- SHAMAN
    [BS['Earth Shock']]      = true,
    [BS['Flame Shock']]      = true,
    [BS['Frost Shock']]      = true,
    -- WARLOCK
    [BS['Shadowburn']]       = true,
})

---------------------------------------------------------
-- BUFFS:
---------------------------------------------------------

uc.RegisterTable("InterruptsToTrack", {
    -- MISC
    -- DRUID
    -- HUNTER
    -- MAGE
    -- PALADIN
    -- PRIEST
    -- ROGUE
    [BS['Kick']] = true,
    -- SHAMAN
    [BS['Earth Shock']] = true,
    -- WARLOCK
    -- WARRIOR
    [BS['Pummel']] = true, [BS['Shield Bash']] = true,
})


uc.RegisterTable("InterruptBuffsToTrack", {
    [BS["Aspect of the Cheetah"]]    = true,
    [BS["Aspect of the Hawk"]]       = true,
    [BS["Aspect of the Monkey"]]     = true,
    [BS["Bash"]]                     = true,
    [BS["Bear Form"]]                = true,
    [BS["Blackout"]]                 = true,
    [BS["Blind"]]                    = true,
    [BS["Blink"]]                    = true,
    [BS["Cat Form"]]                 = true,
    [BS["Charge Stun"]]              = true,
    [BS["Cheap Shot"]]               = true,
    [BS["Concussion Blow"]]          = true,
    [BS["Counterspell - Silenced"]]  = true,
    [BS["Death Coil"]]               = true,
    [BS["Dire Bear Form"]]           = true,
    [BS["Divine Intervention"]]      = true,
    [BS["Divine Protection"]]        = true,
    [BS["Divine Shield"]]            = true,
    [BS["Elemental Mastery"]]        = true,
    [BS["Fear"]]                     = true,
    [BS["Feral Charge Effect"]]      = true,
    [BS["Fire Ward"]]                = true,
    [BS["Freezing Trap Effect"]]     = true,
    [BS["Frost Armor"]]              = true,
    [BS["Frost Ward"]]               = true,
    [BS["Ghost Wolf"]]               = true,
    [BS["Gouge"]]                    = true,
    [BS["Hammer of Justice"]]        = true,
    [BS["Ice Armor"]]                = true,
    [BS["Ice Barrier"]]              = true,
    [BS["Ice Block"]]                = true,
    [BS["Impact"]]                   = true,
    [BS["Improved Concussive Shot"]] = true,
    [BS["Inferno Effect"]]           = true,
    [BS["Inner Fire"]]               = true,
    [BS["Intercept Stun"]]           = true,
    [BS["Intimidating Shout"]]       = true,
    [BS["Intimidation"]]             = true,
    [BS["Iron Grenade"]]             = true,
    [BS["Kick - Silenced"]]          = true,
    [BS["Kidney Shot"]]              = true,
    [BS["Lightning Shield"]]         = true,
    [BS["Mace Stun Effect"]]         = true,
    [BS["Mage Armor"]]               = true,
    [BS["Mana Shield"]]              = true,
    [BS["Moonkin Form"]]             = true,
    [BS["Nature's Swiftness"]]       = true,
    [BS["Polymorph"]]                = true,
    [BS["Polymorph: Pig"]]           = true,
    [BS["Polymorph: Turtle"]]        = true,
    [BS["Pounce"]]                   = true,
    [BS["Psychic Scream"]]           = true,
    [BS["Reckless Charge"]]          = true,
    [BS["Revenge Stun"]]             = true,
    [BS["Scatter Shot"]]             = true,
    [BS["Seal of Command"]]          = true,
    [BS["Seal of Righteousness"]]    = true,
    [BS["Seal of the Crusader"]]     = true,
    [BS["Seduction"]]                = true,
    [BS["Shadow Ward"]]              = true,
    [BS["Shield Bash - Silenced"]]   = true,
    [BS["Silence"]]                  = true,
    [BS["Spell Lock"]]               = true,
    [BS["Starfire Stun"]]            = true,
    [BS["Stun"]]                     = true,
    [BS["Thorium Grenade"]]          = true,
    [BS["Tidal Charm"]]              = true,
    [BS["Travel Form"]]              = true,
    [BS["War Stomp"]]                = true,
    [BS["Wyvern Sting"]]             = true,
})

uc.RegisterTable("TimeModifierBuffsToTrack", {
    [BS['Barkskin']]                   = { mod = 1.4, list = { 'all' } },
    [BS['Curse of Tongues']]           = { mod = 1.6, list = { 'all' } },
    [BS['Mind-numbing Poison']]        = { mod = 1.6, list = { 'all' } },
    [BS['Mind-numbing Poison II']]     = { mod = 1.6, list = { 'all' } },
    [BS['Mind-numbing Poison III']]    = { mod = 1.6, list = { 'all' } },
--    [BS['Fang of the Crystal Spider']] = { mod = 1.1, list = { 'all' } },
    [BS["Nature\'s Swiftness"]]        = { mod = 0.1,
        list = { -- SHAMAN
            BS['Chain Heal'],
            BS['Chain Lightning'],
            BS['Far Sight'],
            BS['Ghost Wolf'],
            BS['Healing Wave'],
            BS['Lesser Healing Wave'],
            BS['Lightning Bolt'],
            -- DRUID
            BS['Entangling Roots'],
            BS['Healing Touch'],
            BS['Hibernate'],
            BS['Rebirth'],
            BS['Regrowth'],
            BS['Soothe Animal'],
            BS['Wrath'], }
    },
    [BS['Rapid Fire']]                 = { mod = .6, list = { 'Aimed Shot' } },
    [BS['Shadow Trance']]              = { mod = 0, list = { BS['Shadow Bolt'] } },
    [BS['Fel Domination']]             = { mod = 0.05,
        list = {
            BS['Summon Felhunter'],
            BS['Summon Imp'],
            BS['Summon Succubus'],
            BS['Summon Voidwalker']
        } },

    [BS['Presence of Mind']] = { mod = 0,
        list = {
            BS['Conjure Food'],
            BS['Conjure Water'],
            BS['Conjure Mana Agate'],
            BS['Conjure Mana Citrine'],
            BS['Conjure Mana Jade'],
            BS['Conjure Mana Ruby'],
            BS['Fireball'],
            BS['Frostbolt'],
            BS['Flamestrike'],
            BS['Polymorph'],
            BS['Pyroblast'],
            BS['Scorch'],
        }
    },
    -- [BS['Mind Quickening']]  = { mod = 0.66,
        -- list = {
            -- BS['Fireball'],
            -- BS['Frostbolt'],
            -- BS['Pyroblast'],
            -- BS['Scorch'],
            -- BS['Polymorph'],
            -- BS['Polymorph: Pig'],
            -- BS['Polymorph: Turtle']
        -- } },

})

uc.RegisterTable("BuffsToTrack", {
    -- MISC & MOBS
    [BS['Cannibalize']]          = { duration = 10,display = false, },
    [BS['First Aid']]            = { duration = 8, display = false, },
    [BS['Flee']]                 = { duration = 10, },
    [BS['Free Action']]          = { duration = 30, type = 'magic',prio = 4 },
    [BS['Honorless Target']]     = { duration = 30,display = false, },
    [BS['Invulnerability']]      = { duration = 6,type = 'magic', prio = 5 },
    [BS['Living Free Action']]   = { duration = 5, type = 'magic',prio = 4 },
    [BS['Net-o-Matic']]          = { duration = 10, type = 'physical',prio = 2 },
    [BS['Nimble Reflexes']]      = { duration = 8, },
    [BS['Perception']]           = { duration = 20, },
    [BS['Recently Bandaged']]    = { duration = 60, display = false, },
    [BS["Reckless Charge"]]      = { duration = 12, type = 'magic', prio = 3 },
    [BS["Sleep"]]                = { duration = 12, type = 'magic', prio = 3 },
    [BS['Stoneform']]            = { duration = 8, },
    [BS['Damage Absorb']]        = { duration = 15, display = false, },
    [BS['Tidal Charm']]          = { duration = 3, type = 'magic', prio = 2 },
    [BS['War Stomp']]            = { duration = 2, type = 'physical', prio = 1 },
    [BS['Ward of the Eye']]      = { duration = 6, prio = 3 },
    [BS['Will of the Forsaken']] = { duration = 5, prio = 2 },

    -- ENGINEERING
    [BS["Flash Bomb"]]       = { duration = 10, prio = 2 },
    [BS['Fire Reflector']]   = { duration = 5 },
    [BS['Frost Reflector']]  = { duration = 5 },
    [BS['Shadow Reflector']] = { duration = 5 },
    [BS['Thorium Grenade']]  = { duration = 3, type = 'physical', prio = 2 },
    [BS['Iron Grenade']]     = { duration = 3, type = 'physical', prio = 2 },

    -- DRUID
    [BS['Abolish Poison']]        = { duration = 8, type = 'magic' },
    [BS['Barkskin']]              = { duration = 15, type = 'magic', prio = 2 },
    [BS['Bash']]                  = { duration = 4, type = 'physical', prio = 1 },
    [BS['Dash']]                  = { duration = 15, },
    [BS['Demoralizing Roar']]     = { duration = 30, display = false, },
    [BS['Entangling Roots']]      = { duration = 12, type = 'magic', prio = 1, dr = 'Controlled Root' },
    [BS['Enrage']]                = { duration = 10, display = false, },
    [BS['Feral Charge Effect']]   = { duration = 4, type = 'physical', prio = 1 },
    [BS['Frenzied Regeneration']] = { duration = 10, display = false, },
    [BS['Growl']]                 = { duration = 3, display = false, },
    [BS["Hibernate"]]             = { duration = 20, type = 'magic', prio = 3 },
    [BS['Innervate']]             = { duration = 20, type = 'magic', prio = 2 },
    [BS['Insect Swarm']]          = { duration = 12, display = false, },
    [BS['Moonfire']]              = { duration = 12, display = false, },
    [BS['Nature\'s Grace']]       = { duration = 15, display = false, },
    [BS['Nature\'s Grasp']]       = { type = 'magic', duration = 45 },
    [BS['Pounce']]                = { duration = 2, display = false, },
    [BS['Rake']]                  = { duration = 9, display = false, },
    [BS['Regrowth']]              = { duration = 21, display = false, },
    [BS['Rejuvenation']]          = { duration = 12, display = false, },
    [BS['Rip']]                   = { duration = 12, display = false, },
    [BS['Tiger\'s Fury']]         = { duration = 6, display = false, },
    [BS['Faerie Fire']]           = { duration = 40, display = false, },
    [BS['Faerie Fire (Feral)']]   = { duration = 40, display = false, },

    --[[	HUNTER 	]] --
    [BS['Bestial Wrath']]            = { duration = 18, prio = 2 },
    [BS['Boar Charge']]              = { duration = 1, type = 'physical', prio = 2 },
    [BS['Concussive Shot']]          = { duration = 4, type = 'magic', prio = 1 },
    [BS['Counterattack']]            = { duration = 5, type = 'physical', prio = 1 },
    [BS['Deterrence']]               = { duration = 10, prio = 1 },
    [BS['Eagle Eye']]                = { duration = 60, display = false, },
    [BS['Eyes of the Beast']]        = { duration = 60, display = false, },
    [BS['Immolation Trap Effect']]   = { duration = 15, display = false, },
    [BS['Improved Concussive Shot']] = { duration = 3, type = 'physical', prio = 2 },
    [BS['Improved Wing Clip']]       = { duration = 5, type = 'physical', },
    [BS['Intimidation']]             = { duration = 3, type = 'physical', prio = 1 },
    [BS['Quick Shots']]              = { duration = 12, display = false, },
    [BS['Rapid Fire']]               = { duration = 15, type = 'magic', },
    [BS['Scatter Shot']]             = { duration = 4, type = 'physical', prio = 2 },
    [BS["Scare Beast"]]              = { duration = 10, type = 'magic', prio = 2, dr = 'Fear' },
    [BS['Scorpid Sting']]            = { duration = 20, display = false, },
    [BS['Serpent Sting']]            = { duration = 15, display = false, },
    [BS["Freezing Trap Effect"]]     = { duration = 20, type = 'magic', prio = 3 },
    [BS['Viper Sting']]              = { duration = 8, type = 'poison', prio = 1 },
    [BS['Wing Clip']]                = { duration = 10, type = 'physical', },
    [BS['Wyvern Sting']]             = { duration = 12, type = 'poison', prio = 3 },

    -- MAGE
    [BS['Arcane Power']]            = { duration = 15, display = false, },
    [BS['Blast Wave']]              = { duration = 6, type = 'physical', prio = 1 },
    [BS['Clearcasting']]            = { duration = 15, type = 'magic', },
    [BS['Counterspell - Silenced']] = { duration = 4, type = 'magic', prio = 2 },
    [BS["Cone of Cold"]]            = { duration = 10, type = 'magic', display = false, },
    [BS["Chilled"]]                 = { duration = 7, display = false, },
    [BS['Evocation']]               = { duration = 8, display = false, },
    [BS['Fireball']]                = { duration = 8, display = false, },
    [BS["Frostbite"]]               = { duration = 5, type = 'magic', prio = 1 },
    [BS["Frost Nova"]]              = { duration = 8, type = 'magic', prio = 1, dr = 'Controlled Root' },
    [BS['Frost Ward']]              = { duration = 30, type = 'magic' },
    [BS['Frostbolt']]               = { duration = 10, type = 'magic', display = false, },
    [BS['Fire Ward']]               = { duration = 30, type = 'magic' },
    [BS['Ice Barrier']]				= { 				duration = 60, 	type = 'magic'},
    [BS['Ice Block']]               = { duration = 10, prio = 5 },
    [BS['Impact']]                  = { duration = 2, type = 'physical', prio = 1 },
    [BS['Fire Vulnerability']]      = { duration = 30, display = false, },
    [BS["Polymorph"]]               = { duration = 20, type = 'magic', prio = 3, dr = 'Polymorph' },
    [BS['Polymorph: Pig']]          = { duration = 20, type = 'magic', prio = 3, dr = 'Polymorph' },
    [BS['Polymorph: Turtle']]       = { duration = 20, type = 'magic', prio = 3, dr = 'Polymorph' },
    [BS['Pyroblast']]               = { duration = 12, display = false, },
    [BS['Slow Fall']]               = { duration = 30, display = false, },
    [BS['Winter\'s Chill']]         = { duration = 15, type = 'magic', display = false, },

    -- PALADIN
    [BS['Blessing of Sacrifice']]     = { duration = 30, display = false, },
    [BS['Blessing of Protection']]    = { duration = 10, type = 'magic', prio = 2 },
    [BS['Blessing of Freedom']]       = { duration = 16, type = 'magic' },
    [BS['Divine Protection']]         = { duration = 8, prio = 4 },
    [BS['Divine Shield']]             = { duration = 12, prio = 5 },
    [BS['Forbearance']]               = { duration = 60, display = false, },
    [BS["Hammer of Justice"]]         = { duration = 5, type = 'magic', prio = 1, dr = 'Controlled Stun' },
    [BS['Judgement of the Crusader']] = { duration = 10, type = 'magic', display = false, },
    [BS['Judgement of Justice']]      = { duration = 10, type = 'magic', display = false, },
    [BS['Judgement of Light']]        = { duration = 10, type = 'magic', display = false, },
    [BS['Judgement of Wisdom']]       = { duration = 10, type = 'magic', display = false, },
    [BS['Repentance']]                = { duration = 6, type = 'magic', prio = 3 },
    [BS['Seal of Command']]           = { duration = 30, display = false, },
    [BS['Seal of Justice']]           = { duration = 30, display = false, },
    [BS['Seal of Light']]             = { duration = 30, display = false, },
    [BS['Seal of Righteousness']]     = { duration = 30, display = false, },
    [BS['Seal of the Crusader']]      = { duration = 30, display = false, },
    [BS['Seal of Wisdom']]            = { duration = 30, display = false, },
    [BS['Stun']]                      = { duration = 2, type = 'physical', display = false, },
    [BS['Vengeance']]                 = { duration = 8, display = false, },
    [BS['Vindication']]               = { duration = 10, display = false, },

    -- PRIEST
    [BS['Abolish Disease']]      = { duration = 8, display = false, },
    [BS['Blackout']]             = { duration = 3, type = 'magic', prio = 1 },
    [BS['Devouring Plague']]     = { duration = 24, display = false, },
    [BS['Fade']]                 = { duration = 10, display = false, },
    [BS['Holy Fire']]            = { duration = 10, display = false, },
    [BS['Lightwell Renew']]      = { duration = 10, display = false, },
    [BS['Focused Casting']]      = { duration = 6, display = false, },
    [BS['Mind Flay']]            = { duration = 3, type = 'magic', display = false, },
    [BS['Mind Soothe']]          = { duration = 15, display = false, },
    [BS['Mind Vision']]          = { duration = 60, display = false, },
    [BS['Power Word: Shield']]   = { duration = 30, type = 'magic' },
    [BS['Power Infusion']]       = { duration = 15, type = 'magic' },
    [BS['Psychic Scream']]       = { duration = 8, type = 'physical', prio = 1, dr = 'Fear' },
    [BS['Shadow Vulnerability']] = { duration = 15, display = false },
    [BS['Shadow Word: Pain']]    = { duration = 24, display = false, },
    [BS['Silence']]              = { duration = 5, type = 'magic', prio = 2 },
    [BS['Renew']]                = { duration = 15, display = false, },
    [BS['Weakened Soul']]        = { duration = 15, display = false, },

    --[[	ROGUE 	]] --
    [BS['Adrenaline Rush']]         = { duration = 15, },
    [BS['Blade Flurry']]            = { duration = 15, display = false, },
    [BS['Blind']]                   = { duration = 10, type = 'poison', prio = 3 },
    [BS["Cheap Shot"]]              = { duration = 4, type = 'physical', prio = 1 },
    [BS['Crippling Poison']]        = { duration = 12, type = 'poison', display = false, },
    [BS['Deadly Poison V']]         = { duration = 12, display = false, },
    [BS['Evasion']]                 = { duration = 15, },
    [BS['Expose Armor']]            = { duration = 30, display = false, },
    [BS['Garrote']]                 = { duration = 18, display = false, },
    [BS['Ghostly Strike']]          = { duration = 7, display = false, },
    [BS["Gouge"]]                   = { duration = 5, type = 'physical', prio = 2, dr = 'Disorient' },
    [BS['Hemorrhage']]              = { duration = 15, display = false, },
    [BS['Kick - Silenced']]         = { duration = 2, type = 'physical', prio = 1 },
    [BS['Mind-numbing Poison III']] = { duration = 14, display = false, },
    [BS['Remorseless']]             = { duration = 20, display = false, },
    [BS['Riposte']]                 = { duration = 6, type = 'physical', prio = 1 },
    [BS["Sap"]]                     = { duration = 11, type = 'physical', prio = 3, dr = 'Disorient' },
    [BS['Sprint']]                  = { duration = 15, prio = 1 },
    [BS['Kidney Shot']]             = { duration = 6, type = 'physical', prio = 2, dr = 'Controlled Stun' },
    [BS['Wound Poison IV']]         = { duration = 15, type = 'poison', display = false, },
    [BS['Rupture']]                 = { duration = 16, type = 'physical', display = false, },

    -- SHAMAN
    [BS['Earthbind']]              = { duration = 5, type = 'magic', },
    [BS['Flame Shock']]            = { duration = 12, display = false, },
    [BS['Focused Casting']]        = { duration = 6, display = false, },
    [BS['Frost Shock']]            = { duration = 8, type = 'magic', prio = 1, dr = 'Frost Shock' },
    [BS['Grounding Totem Effect']] = { duration = 10, type = 'magic', prio = 3 },
    [BS['Healing Way']]            = { duration = 15, display = false, },
    [BS['Mana Tide Totem']]        = { duration = 12, },
    [BS['Stormstrike']]            = { duration = 12, display = false, },

    -- WARLOCK
    [BS['Corruption']]          = { duration = 18, display = false, },
    [BS['Curse of Agony']]      = { duration = 24, display = false, },
    [BS['Curse of Exhaustion']] = { duration = 30, type = 'curse', },
    [BS['Curse of Tongues']]    = { duration = 30, type = 'curse', },
    [BS['Curse of Weakness']]    = { duration = 120, type = 'curse', },
    [BS['Death Coil']]          = { duration = 3, type = 'magic', prio = 1 },
    [BS['Drain Life']]          = { duration = 5, display = false, },
    [BS['Drain Mana']]          = { duration = 5, display = false, },
    [BS['Drain Soul']]          = { duration = 15, display = false, },
    [BS['Eye of Kilrogg']]      = { duration = 45, display = false, },
    [BS["Fear"]]                = { duration = 15, type = 'magic', prio = 2, dr = 'Fear' },
    [BS['Health Funnel']]       = { duration = 10, display = false, },
    [BS['Immolate']]            = { duration = 15, type = 'magic', display = false, },
    [BS['Sacrifice']]           = { duration = 30, type = 'magic' },
    [BS['Seduction']]           = { duration = 10, type = 'magic', prio = 3, dr = 'Fear' },
    [BS['Shadowburn']]          = { duration = 5, display = false, },
    [BS['Shadow Trance']]       = { duration = 10, type = 'magic' },
    [BS['Shadow Ward']]         = { duration = 30, type = 'magic' },
    [BS['Siphon Life']]         = { duration = 30, display = false, },

    --[[	WARRRIOR 	]] --
    [BS['Berserker Rage']]         = { duration = 10, },
    [BS['Bloodrage']]              = { duration = 10, display = false, },
    [BS['Blood Craze']]            = { duration = 6, display = false, },
    [BS['Bloodthirst']]            = { duration = 8, display = false, },
    [BS['Challenging Shout']]      = { duration = 6, display = false, },
    [BS['Charge Stun']]            = { duration = 1, type = 'physical', prio = 1, dr = 'Controlled Stun' },
    [BS['Concussion Blow']]        = { duration = 5, type = 'physical', prio = 1 },
    [BS['Death Wish']]             = { duration = 30, },
    [BS['Deep Wound']]             = { duration = 12, display = false, },
    [BS['Demoralizing Shout']]     = { duration = 30, display = false, },
    [BS['Disarm']]                 = { duration = 8, type = 'physical', prio = 1 },
    [BS['Enrage']]                 = { duration = 12, display = false, },
    [BS['Hamstring']]              = { duration = 15, type = 'physical', prio = 1 },
    [BS['Improved Hamstring']]     = { duration = 5, type = 'physical', prio = 2 },
    [BS['Intercept Stun']]         = { duration = 3, type = 'physical', prio = 1, dr = 'Controlled Stun' },
    [BS['Intimidating Shout']]     = { duration = 8, type = 'physical', prio = 2, dr = 'Fear' },
    [BS['Last Stand']]             = { duration = 20, },
    [BS['Mace Stun Effect']]       = { duration = 3, type = 'physical', prio = 1, },
    [BS['Mocking Blow']]           = { duration = 6, display = false, },
    [BS['Mortal Strike']]          = { duration = 10, type = 'physical' },
    [BS['Rend']]                   = { duration = 21, display = false, },
    [BS['Retaliation']]            = { duration = 15, prio = 2, },
    [BS['Shield Bash - Silenced']] = { duration = 3, type = 'magic', prio = 2 },
    [BS['Shield Block']]           = { duration = 5, display = false, },
    [BS['Shield Wall']]            = { duration = 10, prio = 2 },
    [BS['Sweeping Strikes']]       = { duration = 20, display = false, },
    [BS['Thunder Clap']]           = { duration = 30, display = false, },
    [BS['Sunder Armor']]           = { duration = 30 },
    [BS['Piercing Howl']]          = { duration = 6, type = 'physical', prio = 2},
    -- HUNTER
    --[BS['Feign Death']]            = { [[Interface\Icons\Ability_rogue_feigndeath]], 360 },
    -- ROGUE
    -- [BS['Vanish']]                 = { [[Interface\Icons\Ability_vanish]], 10 },

})
uc.RegisterTable("UniqueDebuffs", {
    [BS['Kidney Shot']] = {  cp = { 2, 3, 4, 5, 6 }, type = 'physical', prio = 2, dr = 'Controlled Stun' },
    [BS['Rupture']]     = {  cp = { 8, 10, 12, 14, 16 }, type = 'physical', display = false, },
    [BS['Sunder Armor']]           = { duration = 30 },
    [BS['Entangling Roots']]  = {r = {12, 15, 18, 21, 24, 27}, type = 'magic', prio = 1, dr = 'Controlled Root'},
    [BS["Polymorph"]]               = { r = {20, 30, 40, 50}, type = 'magic', prio = 3, dr = 'Polymorph' },
})
--
uc.RegisterTable("DebuffRefreshingSpells", {
    -- DRUID
    [BS['Moonfire']] = { BS['Moonfire'] },
    [BS['Rake']] = { BS['Rake'] },
    [BS['Regrowth']] = { BS['Regrowth'], },
    -- HUNTER
    [BS['Wing Clip']] = { BS['Wing Clip'], },
    -- MAGE
    [BS['Fireball']] = { BS['Fireball'], },
    [BS['Blizzard']] = { BS['Winter\'s Chill'], },
    [BS['Cone of Cold']] = { BS['Winter\'s Chill'], },
    [BS['Frost Nova']] = { BS['Winter\'s Chill'], },
    [BS['Frostbolt']] = { BS['Frostbolt'], BS['Winter\'s Chill'], },
    [BS['Scorch']] = { BS['Improved Scorch'], },
    -- PALADIN
    [BS['Judgement of the Crusader']] = { BS['Judgement of the Crusader'], },
    [BS['Judgement of Justice']] = { BS['Judgement of Justice'], },
    [BS['Judgement of Light']] = { BS['Judgement of Light'], },
    [BS['Judgement of Wisdom']] = { BS['Judgement of Wisdom'], },
    -- PRIEST
    [BS['Holy Fire']] = { BS['Holy Fire'], },
    [BS['Mind Flay']] = { BS['Shadow Vulnerability'], },
    [BS['Mind Blast']] = { BS['Shadow Vulnerability'], },
    [BS['Shadow Vulnerability']] = { BS['Shadow Vulnerability'], },
    [BS['Weakened Soul']] = { BS['Power Word: Shield'], },
    -- ROGUE
    [BS['Hemorrhage']] = { BS['Hemorrhage'], },
    [BS['Wound Poison IV']] = { BS['Wound Poison IV'], },
    [BS['Deadly Poison V']] = { BS['Deadly Poison V'], },
    -- SHAMAN
    [BS['Flame Shock']] = { BS['Flame Shock'], },
    [BS['Frost Shock']] = { BS['Frost Shock'], },
    -- WARLOCK
    [BS['Immolate']] = { BS['Immolate'], },
    -- WARRRIOR
    [BS['Hamstring']] = { BS['Hamstring'], },
    [BS['Mortal Strike']] = { BS['Mortal Strike'], },
    [BS['Sunder Armor']]  = { BS['Sunder Armor'], },
})

uc.RegisterTable("RootSnares", {
    -- MISC
    [BS['Net-o-Matic']] = true,
    -- DRUID
    [BS['Entangling Roots']] = true,
    [BS['Feral Charge Effect']] = true,
    -- HUNTER
    [BS['Concussive Shot']] = true,
    [BS['Wing Clip']] = true,
    [BS['Improved Wing Clip']] = true,
    -- MAGE
    [BS['Blast Wave']] = true,
    [BS["Cone of Cold"]] = true,
    [BS["Frostbite"]] = true,
    [BS["Frost Nova"]] = true,
    [BS['Frostbolt']] = true,
    -- PALADIN
    -- PRIEST
    [BS['Mind Flay']] = true,
    -- ROGUE
    -- SHAMAN
    [BS['Frost Shock']] = true,
    -- WARRRIOR
    [BS['Hamstring']] = true,
    [BS['Improved Hamstring']] = true,
})

--------------------------------
----- HELP:
--------------------------------

uc.RegisterTable("SpellSchoolColors",
	{
		['physical'] 	= {.9, .9, 0},
		['arcane'] 		= {.9, .4, .9},
		['fire']		= {.9, .4, 0},
		['nature'] 		= {.3, .9, .2},
		['frost'] 		= {.4,.9, .9},
		['shadow'] 		= {.4, .4, .9},
		['holy'] 		= {.9, .4, .9}
	}
)