
if GetLocale() == "ruRU" then

	if not ParserLibOptimizer then
		ParserLibOptimizer = {
			AURAADDEDOTHERHARMFUL = "находится",
			AURAADDEDOTHERHELPFUL = "получает",
			AURAADDEDSELFHARMFUL = "находитесь",
			AURAADDEDSELFHELPFUL = "получаете",
			AURAAPPLICATIONADDEDOTHERHARMFUL = "находится",
			AURAAPPLICATIONADDEDOTHERHELPFUL = "получает",
			AURAAPPLICATIONADDEDSELFHARMFUL = "находитесь",
			AURAAPPLICATIONADDEDSELFHELPFUL = "получаете",
			AURADISPELOTHER = "теряет",
			AURADISPELSELF = "теряете",
			AURAREMOVEDOTHER = "заканчивается",
			AURAREMOVEDSELF = "заканчивается",
			COMBATHITCRITOTHEROTHER = "критический",
			COMBATHITCRITOTHERSELF = "критический",
			COMBATHITCRITSELFOTHER = "критический",
			COMBATHITCRITSELFSELF = "критический",
			COMBATHITCRITSCHOOLOTHEROTHER = "критический",
			COMBATHITCRITSCHOOLOTHERSELF = "критический",
			COMBATHITCRITSCHOOLSELFOTHER = "критический",
			COMBATHITCRITSCHOOLSELFSELF = "критический",
			COMBATHITOTHEROTHER = "наносит",
			COMBATHITOTHERSELF = "наносит",
			COMBATHITSELFOTHER = "наносите",
			COMBATHITSELFSELF = "наносите",
			COMBATHITSCHOOLOTHEROTHER = "наносит",
			COMBATHITSCHOOLOTHERSELF = "наносит",
			COMBATHITSCHOOLSELFOTHER = "наносите",
			COMBATHITSCHOOLSELFSELF = "наносите",
			DAMAGESHIELDOTHEROTHER = "отражает",
			DAMAGESHIELDOTHERSELF = "отражает",
			DAMAGESHIELDSELFOTHER = "отражаете",
			DISPELFAILEDOTHEROTHER = "развеять",
			DISPELFAILEDOTHERSELF = "развеять",
			DISPELFAILEDSELFOTHER = "развеять",
			DISPELFAILEDSELFSELF = "развеять",
			HEALEDCRITOTHEROTHER = "критический",
			HEALEDCRITOTHERSELF = "критический",
			HEALEDCRITSELFOTHER = "критический",
			HEALEDCRITSELFSELF = "критический",
			HEALEDOTHEROTHER = "исцеляет",
			HEALEDOTHERSELF = "исцеляет",
			HEALEDSELFOTHER = "исцеляет",
			HEALEDSELFSELF = "исцеляет",
			IMMUNESPELLOTHEROTHER = "невосприимчивостью",
			IMMUNESPELLSELFOTHER = "невосприимчивостью",
			IMMUNESPELLOTHERSELF = "невосприимчивы",
			IMMUNESPELLSELFSELF = "невосприимчивостью",
			ITEMENCHANTMENTADDOTHEROTHER = "накладывает",
			ITEMENCHANTMENTADDOTHERSELF = "накладывает",
			ITEMENCHANTMENTADDSELFOTHER = "накладываете",
			ITEMENCHANTMENTADDSELFSELF = "накладываете",
			MISSEDOTHEROTHER = "попадает",
			MISSEDOTHERSELF = "попадает",
			MISSEDSELFOTHER = "попадаете",
			MISSEDSELFSELF = "попадаете",
			OPEN_LOCK_OTHER = "выполняет",
			OPEN_LOCK_SELF = "выполняете",
			PARTYKILLOTHER = "убивает",
			PERIODICAURADAMAGEOTHEROTHER = "получает",
			PERIODICAURADAMAGEOTHERSELF = "получаете",
			PERIODICAURADAMAGESELFOTHER = "получает",
			PERIODICAURADAMAGESELFSELF = "получаете",
			PERIODICAURAHEALOTHEROTHER = "получает",
			PERIODICAURAHEALOTHERSELF = "получаете",
			PERIODICAURAHEALSELFOTHER = "получает",
			PERIODICAURAHEALSELFSELF = "получаете",
			POWERGAINOTHEROTHER = "получает",
			POWERGAINOTHERSELF = "получаете",
			POWERGAINSELFSELF = "получаете",
			POWERGAINSELFOTHER = "получает",
			PROCRESISTOTHEROTHER = "сопротивляется",
			PROCRESISTOTHERSELF = "сопротивляетесь",
			PROCRESISTSELFOTHER = "сопротивляется",
			PROCRESISTSELFSELF = "сопротивляетесь",
			SIMPLECASTOTHEROTHER = "применяет",
			SIMPLECASTOTHERSELF = "применяет",
			SIMPLECASTSELFOTHER = "применяете",
			SIMPLECASTSELFSELF = "применяете",
			SIMPLEPERFORMOTHEROTHER = "выполняет",
			SIMPLEPERFORMOTHERSELF = "выполняет",
			SIMPLEPERFORMSELFOTHER = "выполняете",
			SIMPLEPERFORMSELFSELF = "выполняете",
			SPELLBLOCKEDOTHEROTHER = "блокирует",
			SPELLBLOCKEDOTHERSELF = "блокируете",
			SPELLBLOCKEDSELFOTHER = "блокирует",
			SPELLBLOCKEDSELFSELF = "блокируете",
			SPELLCASTOTHERSTART = "начинает",
			SPELLCASTSELFSTART = "начинаете",
			SPELLDEFLECTEDOTHEROTHER = "отклоняет",
			SPELLDEFLECTEDOTHERSELF = "отклоняете",
			SPELLDEFLECTEDSELFOTHER = "отклоняет",
			SPELLDEFLECTEDSELFSELF = "отклоняете",
			SPELLDODGEDOTHEROTHER = "уклоняется",
			SPELLDODGEDOTHERSELF = "уклоняетесь",
			SPELLDODGEDSELFOTHER = "уклоняется",
			SPELLEVADEDOTHEROTHER = "достигает",
			SPELLEVADEDOTHERSELF = "достигает",
			SPELLEVADEDSELFOTHER = "достигает",
			SPELLEVADEDSELFSELF = "достигает",
			SPELLEXTRAATTACKSOTHER = "дополнительных",
			SPELLEXTRAATTACKSOTHER_SINGULAR = "дополнительную",
			SPELLEXTRAATTACKSSELF = "дополнительных",
			SPELLEXTRAATTACKSSELF_SINGULAR = "дополнительную",
			SPELLFAILCASTSELF = "удалось",
			SPELLFAILPERFORMSELF = "удалось",
			SPELLIMMUNEOTHEROTHER = "невосприимчивостью",
			SPELLIMMUNEOTHERSELF = "невосприимчивостью",
			SPELLIMMUNESELFOTHER = "невосприимчивостью",
			SPELLIMMUNESELFSELF = "невосприимчивостью",
			SPELLINTERRUPTOTHEROTHER = "прерывает",
			SPELLINTERRUPTOTHERSELF = "прерывает",
			SPELLINTERRUPTSELFOTHER = "прервали",
			SPELLLOGABSORBOTHEROTHER = "поглощает",
			SPELLLOGABSORBOTHERSELF = "поглощаете",
			SPELLLOGABSORBSELFOTHER = "поглощает",
			SPELLLOGABSORBSELFSELF = "поглощаете",
			SPELLLOGCRITOTHEROTHER = "критический",
			SPELLLOGCRITOTHERSELF = "критический",
			SPELLLOGCRITSCHOOLOTHEROTHER = "критический",
			SPELLLOGCRITSCHOOLOTHERSELF = "критический",
			SPELLLOGCRITSCHOOLSELFOTHER = "критический",
			SPELLLOGCRITSCHOOLSELFSELF = "критический",
			SPELLLOGCRITSELFOTHER = "критический",
			SPELLLOGOTHEROTHER = "наносит",
			SPELLLOGOTHERSELF = "наносит",
			SPELLLOGOTHERSELF = "наносит",
			SPELLLOGSCHOOLOTHEROTHER = "наносит",
			SPELLLOGSCHOOLOTHERSELF = "наносит",
			SPELLLOGSCHOOLSELFOTHER = "наносит",
			SPELLLOGSCHOOLSELFSELF = "наносит",
			SPELLLOGSELFOTHER = "наносит",
			SPELLMISSOTHEROTHER = "попадает",
			SPELLMISSOTHERSELF = "попадает",
			SPELLMISSSELFOTHER = "попадает",
			SPELLPARRIEDOTHEROTHER = "парирует",
			SPELLPARRIEDOTHERSELF = "парируете",
			SPELLPARRIEDSELFOTHER = "парирует",
			SPELLPERFORMOTHERSTART = "начинает",
			SPELLPERFORMSELFSTART = "начинаете",
			SPELLPOWERDRAINOTHEROTHER = "отнимает",
			SPELLPOWERDRAINOTHERSELF = "отнимает",
			SPELLPOWERDRAINSELFOTHER = "отнимает",
			SPELLPOWERLEECHOTHEROTHER = "отнимает",
			SPELLPOWERLEECHOTHERSELF = "отнимает",
			SPELLPOWERLEECHSELFOTHER = "отнимает",
			SPELLREFLECTOTHEROTHER = "отражает",
			SPELLREFLECTOTHERSELF = "отражаете",
			SPELLREFLECTSELFOTHER = "отражает",
			SPELLREFLECTSELFSELF = "отражаете",
			SPELLRESISTOTHEROTHER = "сопротивляется",
			SPELLRESISTOTHERSELF = "сопротивляетесь",
			SPELLRESISTSELFOTHER = "сопротивляется",
			SPELLRESISTSELFSELF = "сопротивляетесь",
			SPELLSPLITDAMAGESELFOTHER = "наносит",
			SPELLSPLITDAMAGEOTHEROTHER = "наносит",
			SPELLSPLITDAMAGEOTHERSELF = "наносит",
			SPELLTERSEPERFORM_OTHER = "выполняет",
			SPELLTERSEPERFORM_SELF = "выполняете",
			SPELLTERSE_OTHER = "применяет",
			SPELLTERSE_SELF = "применяете",
			VSABSORBOTHEROTHER = "поглощает",
			VSABSORBOTHERSELF = "поглощаете",
			VSABSORBSELFOTHER = "поглощает",
			VSBLOCKOTHEROTHER = "блокирует",
			VSBLOCKOTHERSELF = "блокируете",
			VSBLOCKSELFOTHER = "блокирует",
			VSBLOCKSELFSELF = "блокируете",
			VSDEFLECTOTHEROTHER = "отклоняет",
			VSDEFLECTOTHERSELF = "отклоняете",
			VSDEFLECTSELFOTHER = "отклоняет",
			VSDEFLECTSELFSELF = "отклоняете",
			VSDODGEOTHEROTHER = "уклоняется",
			VSDODGEOTHERSELF = "уклоняетесь",
			VSDODGESELFOTHER = "уклоняется",
			VSDODGESELFSELF = "уклоняетесь",
			VSENVIRONMENTALDAMAGE_FALLING_OTHER = "падает",
			VSENVIRONMENTALDAMAGE_FALLING_SELF = "падаете",
			VSENVIRONMENTALDAMAGE_FIRE_OTHER = "огня",
			VSENVIRONMENTALDAMAGE_FIRE_SELF = "огня",
			VSENVIRONMENTALDAMAGE_LAVA_OTHER = "лаву",
			VSENVIRONMENTALDAMAGE_LAVA_SELF = "лаву",
			VSEVADEOTHEROTHER = "достигает",
			VSEVADEOTHERSELF = "достигает",
			VSEVADESELFOTHER = "достигает",
			VSEVADESELFSELF = "достигает",
			VSIMMUNEOTHEROTHER = "невосприимчивостью",
			VSIMMUNEOTHERSELF = "неуязвимы",
			VSIMMUNESELFOTHER = "невосприимчивостью",
			VSPARRYOTHEROTHER = "парирует",
			VSPARRYOTHERSELF = "парируете",
			VSPARRYSELFOTHER = "парирует",
			VSRESISTOTHEROTHER = "сопротивляется",
			VSRESISTOTHERSELF = "сопротивляетесь",
			VSRESISTSELFOTHER = "сопротивляется",
			VSRESISTSELFSELF = "сопротивляетесь",
			VSENVIRONMENTALDAMAGE_FATIGUE_OTHER = "усталости",
			VSENVIRONMENTALDAMAGE_FIRE_OTHER = "огня",
			VSENVIRONMENTALDAMAGE_SLIME_OTHER = "слизь",
			VSENVIRONMENTALDAMAGE_SLIME_SELF = "слизь",
			VSENVIRONMENTALDAMAGE_DROWNING_OTHER = "тонет",
			UNITDIESSELF = "умерли",
			UNITDIESOTHER = "погибает",
			UNITDESTROYEDOTHER = "Уничтожено",
			
		}

	end

end

