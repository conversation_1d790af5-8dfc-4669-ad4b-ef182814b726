﻿--[[ 	Name: LibBabble-Spell-3.0 		Revision: $Rev: 67249 $ 
     	Author(s): ckknight (<EMAIL>) 	Website: http://ckknight.wowinterface.com/
     	Description: A library to provide localizations for spells.  ]]

local MAJOR_VERSION, MINOR_VERSION = "LibBabble-Spell-3.0", tonumber(string.sub("$Revision: 67250 $", 12, -3))

local _G = getfenv()
local format = string.format

_G.LibBabble_Spell_3_0_MINOR = MINOR_VERSION
_G.LibBabble_Spell_3_0_funcs = {}
_G.LibBabble_Spell_3_0_foundLocale = false

table.insert(_G.LibBabble_Spell_3_0_funcs, function(lib)
	-- #AUTODOC_NAMESPACE lib

	lib:SetBaseTranslations {
		["Abolish Disease"] = true,
		["Abolish Poison Effect"] = true,
		["Abolish Poison"] = true,
		["Acid Breath"] = true,
		["Acid of Hakkar"] = true,
		["Acid Spit"] = true,
		["Acid Splash"] = true,
		["Activate MG Turret"] = true,
		["Adrenaline Rush"] = true,
		["Aftermath"] = true,
		["Aggression"] = true,
		["Aimed Shot"] = true,
		["Alchemy"] = true,
		["Ambush"] = true,
		["Amplify Curse"] = true,
		["Amplify Damage"] = true,
		["Amplify Flames"] = true,
		["Amplify Magic"] = true,
		["Ancestral Fortitude"] = true,
		["Ancestral Healing"] = true,
		["Ancestral Knowledge"] = true,
		["Ancestral Spirit"] = true,
		["Anesthetic Poison"] = true,
		["Anger Management"] = true,
		["Anguish"] = true,
		["Anticipation"] = true,
		["Aqua Jet"] = true,
		["Aquatic Form"] = true,
		["Arcane Blast"] = true,
		["Arcane Bolt"] = true,
		["Arcane Bomb"] = true,
		["Arcane Brilliance"] = true,
		["Arcane Concentration"] = true,
		["Arcane Explosion"] = true,
		["Arcane Focus"] = true,
		["Arcane Instability"] = true,
		["Arcane Intellect"] = true,
		["Arcane Meditation"] = true,
		["Arcane Mind"] = true,
		["Arcane Missile"] = true,
		["Arcane Missiles"] = true,
		["Arcane Potency"] = true,
		["Arcane Power"] = true,
		["Arcane Resistance"] = true,
		["Arcane Shot"] = true,
		["Arcane Subtlety"] = true,
		["Arcane Weakness"] = true,
		["Arcing Smash"] = true,
		["Arctic Reach"] = true,
		["Armorsmith"] = true,
		["Arugal's Curse"] = true,
		["Arugal's Gift"] = true,
		["Ascendance"] = true,
		["Aspect of Arlokk"] = true,
		["Aspect of Jeklik"] = true,
		["Aspect of Mar'li"] = true,
		["Aspect of the Beast"] = true,
		["Aspect of the Cheetah"] = true,
		["Aspect of the Hawk"] = true,
		["Aspect of the Monkey"] = true,
		["Aspect of the Pack"] = true,
		["Aspect of the Viper"] = true,
		["Aspect of the Wild"] = true,
		["Aspect of Venoxis"] = true,
		["Astral Recall"] = true,
		["Attack"] = true,
		["Attacking"] = true,
		["Aura of Command"] = true,
		["Aural Shock"] = true,
		["Auto Shot"] = true,
		["Avenger's Shield"] = true,
		["Avenging Wrath"] = true,
		["Avoidance"] = true,
		["Axe Flurry"] = true,
		["Axe Specialization"] = true,
		["Axe Toss"] = true,
		["Backhand"] = true,
		["Backlash"] = true,
		["Backstab"] = true,
		["Bane"] = true,
		["Baneful Poison"] = true,
		["Banish"] = true,
		["Banshee Curse"] = true,
		["Banshee Shriek"] = true,
		["Banshee Wail"] = true,
		["Barbed Sting"] = true,
		["Barkskin Effect"] = true,
		["Barkskin"] = true,
		["Barrage"] = true,
		["Bash"] = true,
		["Basic Campfire"] = true,
		["Battle Shout"] = true,
		["Battle Stance Passive"] = true,
		["Battle Stance"] = true,
		["Bear Form"] = true,
		["Beast Lore"] = true,
		["Beast Slaying"] = true,
		["Beast Training"] = true,
		["Befuddlement"] = true,
		["Bellowing Roar"] = true,
		["Benediction"] = true,
		["Berserker Charge"] = true,
		["Berserker Rage"] = true,
		["Berserker Stance Passive"] = true,
		["Berserker Stance"] = true,
		["Berserking"] = true,
		["Bestial Discipline"] = true,
		["Bestial Swiftness"] = true,
		["Bestial Wrath"] = true,
		["Big Bronze Bomb"] = true,
		["Big Iron Bomb"] = true,
		["Biletoad Infection"] = true,
		["Binding Heal"] = true,
		["Bite"] = true,
		["Black Arrow"] = true,
		["Black Sludge"] = true,
		["Blackout"] = true,
		["Blacksmithing"] = true,
		["Blade Flurry"] = true,
		["Blast Wave"] = true,
		["Blaze"] = true,
		["Blazing Speed"] = true,
		["Blessed Recovery"] = true,
		["Blessing of Blackfathom"] = true,
		["Blessing of Freedom"] = true,
		["Blessing of Kings"] = true,
		["Blessing of Light"] = true,
		["Blessing of Might"] = true,
		["Blessing of Protection"] = true,
		["Blessing of Sacrifice"] = true,
		["Blessing of Salvation"] = true,
		["Blessing of Sanctuary"] = true,
		["Blessing of Shahram"] = true,
		["Blessing of Wisdom"] = true,
		["Blind"] = true,
		["Blinding Powder"] = true,
		["Blink"] = true,
		["Blizzard"] = true,
		["Block"] = true,
		["Blood Craze"] = true,
		["Blood Frenzy"] = true,
		["Blood Funnel"] = true,
		["Blood Fury"] = true,
		["Blood Leech"] = true,
		["Blood Pact"] = true,
		["Blood Siphon"] = true,
		["Blood Tap"] = true,
		["Bloodlust"] = true,
		["Bloodrage"] = true,
		["Bloodthirst"] = true,
		["Boar Charge"] = true,
		["Bomb"] = true,
		["Booming Voice"] = true,
		["Boulder"] = true,
		["Bow Specialization"] = true,
		["Bows"] = true,
		["Brain Wash"] = true,
		["Breath"] = true,
		["Bright Campfire"] = true,
		["Brutal Impact"] = true,
		["Burning Adrenaline"] = true,
		["Burning Soul"] = true,
		["Burning Wish"] = true,
		["Butcher Drain"] = true,
		["Call of Flame"] = true,
		["Call of the Grave"] = true,
		["Call of Thunder"] = true,
		["Call Pet"] = true,
		["Camouflage"] = true,
		["Cannibalize"] = true,
		["Cat Form"] = true,
		["Cataclysm"] = true,
		["Cause Insanity"] = true,
		["Chain Bolt"] = true,
		["Chain Burn"] = true,
		["Chain Heal"] = true,
		["Chain Lightning"] = true,
		["Chained Bolt"] = true,
		["Chains of Ice"] = true,
		["Challenging Roar"] = true,
		["Challenging Shout"] = true,
		["Charge Rage Bonus Effect"] = true,
		["Charge Stun"] = true,
		["Charge"] = true,
		["Cheap Shot"] = true,
		["Chilled"] = true,
		["Chilling Touch"] = true,
		["Chromatic Infusion"] = true,
		["Circle of Healing"] = true,
		["Claw"] = true,
		["Cleanse Nova"] = true,
		["Cleanse"] = true,
		["Clearcasting"] = true,
		["Cleave"] = true,
		["Clever Traps"] = true,
		["Cloak of Shadows"] = true,
		["Clone"] = true,
		["Closing"] = true,
		["Cloth"] = true,
		["Coarse Sharpening Stone"] = true,
		["Cobra Reflexes"] = true,
		["Cold Blood"] = true,
		["Cold Snap"] = true,
		["Combat Endurance"] = true,
		["Combustion"] = true,
		["Command"] = true,
		["Commanding Shout"] = true,
		["Concentration Aura"] = true,
		["Concussion Blow"] = true,
		["Concussion"] = true,
		["Concussive Shot"] = true,
		["Cone of Cold"] = true,
		["Conflagrate"] = true,
		["Conjure Food"] = true,
		["Conjure Mana Agate"] = true,
		["Conjure Mana Citrine"] = true,
		["Conjure Mana Jade"] = true,
		["Conjure Mana Ruby"] = true,
		["Conjure Water"] = true,
		["Consecrated Sharpening Stone"] = true,
		["Consecration"] = true,
		["Consume Magic"] = true,
		["Consume Shadows"] = true,
		["Consuming Shadows"] = true,
		["Convection"] = true,
		["Conviction"] = true,
		["Cooking"] = true,
		["Corrosive Acid Breath"] = true,
		["Corrosive Ooze"] = true,
		["Corrosive Poison"] = true,
		["Corrupted Blood"] = true,
		["Corruption"] = true,
		["Counterattack"] = true,
		["Counterspell - Silenced"] = true,
		["Counterspell"] = true,
		["Cower"] = true,
		["Create Firestone (Greater)"] = true,
		["Create Firestone (Lesser)"] = true,
		["Create Firestone (Major)"] = true,
		["Create Firestone"] = true,
		["Create Healthstone (Greater)"] = true,
		["Create Healthstone (Lesser)"] = true,
		["Create Healthstone (Major)"] = true,
		["Create Healthstone (Minor)"] = true,
		["Create Healthstone"] = true,
		["Create Soulstone (Greater)"] = true,
		["Create Soulstone (Lesser)"] = true,
		["Create Soulstone (Major)"] = true,
		["Create Soulstone (Minor)"] = true,
		["Create Soulstone"] = true,
		["Create Spellstone (Greater)"] = true,
		["Create Spellstone (Major)"] = true,
		["Create Spellstone (Master)"] = true,
		["Create Spellstone"] = true,
		["Creeper Venom"] = true,
		["Creeping Mold"] = true,
		["Cripple"] = true,
		["Crippling Poison II"] = true,
		["Crippling Poison"] = true,
		["Critical Mass"] = true,
		["Crossbows"] = true,
		["Crowd Pummel"] = true,
		["Cruelty"] = true,
		["Crusader Aura"] = true,
		["Crusader Strike"] = true,
		["Crusader's Wrath"] = true,
		["Crystal Charge"] = true,
		["Crystal Flash"] = true,
		["Crystal Force"] = true,
		["Crystal Gaze"] = true,
		["Crystal Restore"] = true,
		["Crystal Spire"] = true,
		["Crystal Ward"] = true,
		["Crystal Yield"] = true,
		["Crystalline Slumber"] = true,
		["Cultivate Packet of Seeds"] = true,
		["Cultivation"] = true,
		["Cure Disease"] = true,
		["Cure Poison"] = true,
		["Curse of Agony"] = true,
		["Curse of Blood"] = true,
		["Curse of Doom Effect"] = true,
		["Curse of Doom"] = true,
		["Curse of Exhaustion"] = true,
		["Curse of Idiocy"] = true,
		["Curse of Mending"] = true,
		["Curse of Recklessness"] = true,
		["Curse of Shadow"] = true,
		["Curse of the Deadwood"] = true,
		["Curse of the Elemental Lord"] = true,
		["Curse of the Elements"] = true,
		["Curse of the Fallen Magram"] = true,
		["Curse of Tongues"] = true,
		["Curse of Tuten'kash"] = true,
		["Curse of Weakness"] = true,
		["Cursed Blood"] = true,
		["Cyclone"] = true,
		["Dagger Specialization"] = true,
		["Daggers"] = true,
		["Damage Absorb"] = true,
		["Dampen Magic"] = true,
		["Dark Iron Bomb"] = true,
		["Dark Mending"] = true,
		["Dark Offering"] = true,
		["Dark Pact"] = true,
		["Dark Sludge"] = true,
		["Darkness"] = true,
		["Dash"] = true,
		["Dazed"] = true,
		["Deadly Poison II"] = true,
		["Deadly Poison III"] = true,
		["Deadly Poison IV"] = true,
		["Deadly Poison V"] = true,
		["Deadly Poison"] = true,
		["Deadly Throw"] = true,
		["Death Coil"] = true,
		["Death Wish"] = true,
		["Decayed Strength"] = true,
		["Deep Sleep"] = true,
		["Deep Slumber"] = true,
		["Deep Wound"] = true,
		["Deep Wounds"] = true,
		["Defense"] = true,
		["Defensive Stance Passive"] = true,
		["Defensive Stance"] = true,
		["Defensive State 2"] = true,
		["Defensive State"] = true,
		["Defiance"] = true,
		["Deflection"] = true,
		["Delusions of Jin'do"] = true,
		["Demon Armor"] = true,
		["Demon Skin"] = true,
		["Demonic Embrace"] = true,
		["Demonic Frenzy"] = true,
		["Demonic Sacrifice"] = true,
		["Demoralizing Roar"] = true,
		["Demoralizing Shout"] = true,
		["Dense Sharpening Stone"] = true,
		["Desperate Prayer"] = true,
		["Destructive Reach"] = true,
		["Detect Greater Invisibility"] = true,
		["Detect Invisibility"] = true,
		["Detect Lesser Invisibility"] = true,
		["Detect Magic"] = true,
		["Detect Traps"] = true,
		["Detect"] = true,
		["Deterrence"] = true,
		["Detonation"] = true,
		["Devastate"] = true,
		["Devastation"] = true,
		["Devotion Aura"] = true,
		["Devour Magic Effect"] = true,
		["Devour Magic"] = true,
		["Devouring Plague"] = true,
		["Diamond Flask"] = true,
		["Dimensional Ripper - Everlook"] = true,
		["Diplomacy"] = true,
		["Dire Bear Form"] = true,
		["Dire Growl"] = true,
		["Disarm Trap"] = true,
		["Disarm"] = true,
		["Disease Cleansing Totem"] = true,
		["Disease Cloud"] = true,
		["Diseased Shot"] = true,
		["Diseased Slime"] = true,
		["Diseased Spit"] = true,
		["Disenchant"] = true,
		["Disengage"] = true,
		["Disjunction"] = true,
		["Dismiss Pet"] = true,
		["Dispel Magic"] = true,
		["Distract"] = true,
		["Distracting Pain"] = true,
		["Distracting Shot"] = true,
		["Dive"] = true,
		["Divine Favor"] = true,
		["Divine Fury"] = true,
		["Divine Illumination"] = true,
		["Divine Intellect"] = true,
		["Divine Intervention"] = true,
		["Divine Protection"] = true,
		["Divine Shield"] = true,
		["Divine Spirit"] = true,
		["Divine Strength"] = true,
		["Diving Sweep"] = true,
		["Dodge"] = true,
		["Dominate Mind"] = true,
		["Dragon's Breath"] = true,
		["Dragonscale Leatherworking"] = true,
		["Drain Life"] = true,
		["Drain Mana"] = true,
		["Drain Soul"] = true,
		["Dredge Sickness"] = true,
		["Drink Minor Potion"] = true,
		["Drink"] = true,
		["Druid's Slumber"] = true,
		["Dual Wield Specialization"] = true,
		["Dual Wield"] = true,
		["Duel"] = true,
		["Dust Field"] = true,
		["Dynamite"] = true,
		["Eagle Eye"] = true,
		["Earth Elemental Totem"] = true,
		["Earthbind"] = true,
		["Earth Shield"] = true,
		["Earth Shock"] = true,
		["Earthbind Totem"] = true,
		["Earthborer Acid"] = true,
		["Earthgrab Totem"] = true,
		["Earthgrab"] = true,
		["Efficiency"] = true,
		["Electric Discharge"] = true,
		["Electrified Net"] = true,
		["Elemental Fire"] = true,
		["Elemental Focus"] = true,
		["Elemental Fury"] = true,
		["Elemental Leatherworking"] = true,
		["Elemental Mastery"] = true,
		["Elemental Precision"] = true,
		["Elemental Sharpening Stone"] = true,
		["Elune's Grace"] = true,
		["Elusiveness"] = true,
		["Emberstorm"] = true,
		["Enamored Water Spirit"] = true,
		["Enchanting"] = true,
		["Endurance Training"] = true,
		["Endurance"] = true,
		["Engineering Specialization"] = true,
		["Engineering"] = true,
		["Enrage"] = true,
		["Enriched Manna Biscuit"] = true,
		["Enslave Demon"] = true,
		["Entangling Roots"] = true,
		["Entrapment"] = true,
		["Enveloping Web"] = true,
		["Enveloping Webs"] = true,
		["Enveloping Winds"] = true,
		["Envenom"] = true,
		["Ephemeral Power"] = true,
		["Escape Artist"] = true,
		["Essence of Sapphiron"] = true,
		["Evasion"] = true,
		["Eventide"] = true,
		["Eviscerate"] = true,
		["Evocation"] = true,
		["Execute"] = true,
		["Exorcism"] = true,
		["Expansive Mind"] = true,
		["Explode"] = true,
		["Exploding Shot"] = true,
		["Exploit Weakness"] = true,
		["Explosive Shot"] = true,
		["Explosive Trap Effect"] = true,
		["Explosive Trap"] = true,
		["Expose Armor"] = true,
		["Expose Weakness"] = true,
		["Eye for an Eye"] = true,
		["Eye of Kilrogg"] = true,
		["Eyes of the Beast"] = true,
		["Fade"] = true,
		["Faerie Fire (Feral)"] = true,
		["Faerie Fire"] = true,
		["Far Sight"] = true,
		["Fatal Bite"] = true,
		["Fear Ward"] = true,
		["Fear"] = true,
		["Feed Pet"] = true,
		["Feedback"] = true,
		["Feign Death"] = true,
		["Feint"] = true,
		["Fel Armor"] = true,
		["Fel Concentration"] = true,
		["Fel Domination"] = true,
		["Fel Intellect"] = true,
		["Fel Stamina"] = true,
		["Fel Stomp"] = true,
		["Felfire"] = true,
		["Feline Grace"] = true,
		["Feline Swiftness"] = true,
		["Feral Aggression"] = true,
		["Feral Charge"] = true,
		["Feral Charge Effect"] = true,
		["Feral Instinct"] = true,
		["Ferocious Bite"] = true,
		["Ferocity"] = true,
		["Fetish"] = true,
		["Fevered Fatigue"] = true,
		["Fevered Plague"] = true,
		["Fiery Burst"] = true,
		["Find Herbs"] = true,
		["Find Minerals"] = true,
		["Find Treasure"] = true,
		["Fire Blast"] = true,
		["Fire Elemental Totem"] = true,
		["Fire Nova Totem"] = true,
		["Fire Nova"] = true,
		["Fire Power"] = true,
		["Fire Reflector"] = true,
		["Fire Resistance Aura"] = true,
		["Fire Resistance Totem"] = true,
		["Fire Resistance"] = true,
		["Fire Shield Effect II"] = true,
		["Fire Shield Effect III"] = true,
		["Fire Shield Effect IV"] = true,
		["Fire Shield Effect"] = true,
		["Fire Shield II"] = true,
		["Fire Shield"] = true,
		["Fire Storm"] = true,
		["Fire Vulnerability"] = true,
		["Fire Ward"] = true,
		["Fire Weakness"] = true,
		["Fireball Volley"] = true,
		["Fireball"] = true,
		["Firebolt"] = true,
		["First Aid"] = true,
		["Fishing Poles"] = true,
		["Fishing"] = true,
		["Fist of Ragnaros"] = true,
		["Fist Weapon Specialization"] = true,
		["Fist Weapons"] = true,
		["Flame Buffet"] = true,
		["Flame Cannon"] = true,
		["Flame Lash"] = true,
		["Flame Shock"] = true,
		["Flame Spike"] = true,
		["Flame Spray"] = true,
		["Flame Throwing"] = true,
		["Flames of Shahram"] = true,
		["Flamespit"] = true,
		["Flamestrike"] = true,
		["Flamethrower"] = true,
		["Flametongue Totem"] = true,
		["Flametongue Weapon"] = true,
		["Flare"] = true,
		["Flash Bomb"] = true,
		["Flash Heal"] = true,
		["Flash of Light"] = true,
		["Flee"] = true,
		["Flight Form"] = true,
		["Flurry"] = true,
		["Focused Casting"] = true,
		["Focused Mind"] = true,
		["Food"] = true,
		["Forbearance"] = true,
		["Force of Nature"] = true,
		["Force of Will"] = true,
		["Force Punch"] = true,
		["Force Reactive Disk"] = true,
		["Forked Lightning"] = true,
		["Forsaken Skills"] = true,
		["Frailty"] = true,
		["Free Action"] = true,
		["Freeze Solid"] = true,
		["Freezing Trap Effect"] = true,
		["Freezing Trap"] = true,
		["Frenzied Regeneration"] = true,
		["Frenzy"] = true,
		["Frost Armor"] = true,
		["Frost Breath"] = true,
		["Frost Channeling"] = true,
		["Frost Nova"] = true,
		["Frost Reflector"] = true,
		["Frost Resistance Aura"] = true,
		["Frost Resistance Totem"] = true,
		["Frost Resistance"] = true,
		["Frost Shock"] = true,
		["Frost Shot"] = true,
		["Frost Trap Aura"] = true,
		["Frost Trap"] = true,
		["Frost Ward"] = true,
		["Frost Warding"] = true,
		["Frost Weakness"] = true,
		["Frostbite"] = true,
		["Frostbolt Volley"] = true,
		["Frostbolt"] = true,
		["Frostbrand Weapon"] = true,
		["Furbolg Form"] = true,
		["Furious Howl"] = true,
		["Furor"] = true,
		["Fury of Ragnaros"] = true,
		["Gahz'ranka Slam"] = true,
		["Gahz'rilla Slam"] = true,
		["Garrote"] = true,
		["Gehennas' Curse"] = true,
		["Generic"] = true,
		["Ghost Wolf"] = true,
		["Ghostly Strike"] = true,
		["Gift of Life"] = true,
		["Gift of Nature"] = true,
		["Gift of the Wild"] = true,
		["Gnomish Death Ray"] = true,
		["Goblin Dragon Gun"] = true,
		["Goblin Sapper Charge"] = true,
		["Gouge"] = true,
		["Grace of Air Totem"] = true,
		["Grasping Vines"] = true,
		["Great Stamina"] = true,
		["Greater Blessing of Kings"] = true,
		["Greater Blessing of Light"] = true,
		["Greater Blessing of Might"] = true,
		["Greater Blessing of Salvation"] = true,
		["Greater Blessing of Sanctuary"] = true,
		["Greater Blessing of Wisdom"] = true,
		["Greater Heal"] = true,
		["Grim Reach"] = true,
		["Ground Tremor"] = true,
		["Grounding Totem"] = true,
		["Grounding Totem Effect"] = true,
		["Grovel"] = true,
		["Growl"] = true,
		["Guardian's Favor"] = true,
		["Guillotine"] = true,
		["Gun Specialization"] = true,
		["Guns"] = true,
		["Hail Storm"] = true,
		["Hammer of Justice"] = true,
		["Hammer of Wrath"] = true,
		["Hamstring"] = true,
		["Harass"] = true,
		["Hardiness"] = true,
		["Haunting Spirits"] = true,
		["Hawk Eye"] = true,
		["Head Crack"] = true,
		["Heal"] = true,
		["Healing Circle"] = true,
		["Healing Focus"] = true,
		["Healing Light"] = true,
		["Healing of the Ages"] = true,
		["Healing Stream Totem"] = true,
		["Healing Touch"] = true,
		["Healing Ward"] = true,
		["Healing Wave"] = true,
		["Healing Way"] = true,
		["Health Funnel"] = true,
		["Heart of the Wild"] = true,
		["Hearthstone"] = true,
		["Heavy Sharpening Stone"] = true,
		["Hellfire Effect"] = true,
		["Hellfire"] = true,
		["Hemorrhage"] = true,
		["Herb Gathering"] = true,
		["Herbalism"] = true,
		["Heroic Strike"] = true,
		["Heroism"] = true,
		["Hex of Jammal'an"] = true,
		["Hex of Weakness"] = true,
		["Hex"] = true,
		["Hibernate"] = true,
		["Hi-Explosive Bomb"] = true,
		["Holy Fire"] = true,
		["Holy Light"] = true,
		["Holy Nova"] = true,
		["Holy Power"] = true,
		["Holy Reach"] = true,
		["Holy Shield"] = true,
		["Holy Shock"] = true,
		["Holy Smite"] = true,
		["Holy Specialization"] = true,
		["Holy Strength"] = true,
		["Holy Strike"] = true,
		["Holy Wrath"] = true,
		["Honorless Target"] = true,
		["Hooked Net"] = true,
		["Horse Riding"] = true,
		["Howl of Terror"] = true,
		["Humanoid Slaying"] = true,
		["Hunter's Mark"] = true,
		["Hurricane"] = true,
		["Ice Armor"] = true,
		["Ice Barrier"] = true,
		["Ice Blast"] = true,
		["Ice Block"] = true,
		["Ice Lance"] = true,
		["Ice Nova"] = true,
		["Ice Shards"] = true,
		["Icicle"] = true,
		["Ignite"] = true,
		["Illumination"] = true,
		["Immolate"] = true,
		["Immolation Trap Effect"] = true,
		["Immolation Trap"] = true,
		["Impact"] = true,
		["Impale"] = true,
		["Improved Ambush"] = true,
		["Improved Arcane Explosion"] = true,
		["Improved Arcane Missiles"] = true,
		["Improved Arcane Shot"] = true,
		["Improved Aspect of the Hawk"] = true,
		["Improved Aspect of the Monkey"] = true,
		["Improved Backstab"] = true,
		["Improved Battle Shout"] = true,
		["Improved Berserker Rage"] = true,
		["Improved Blessing of Might"] = true,
		["Improved Blessing of Wisdom"] = true,
		["Improved Blizzard"] = true,
		["Improved Bloodrage"] = true,
		["Improved Chain Heal"] = true,
		["Improved Chain Lightning"] = true,
		["Improved Challenging Shout"] = true,
		["Improved Charge"] = true,
		["Improved Cheap Shot"] = true,
		["Improved Cleave"] = true,
		["Improved Concentration Aura"] = true,
		["Improved Concussive Shot"] = true,
		["Improved Cone of Cold"] = true,
		["Improved Corruption"] = true,
		["Improved Counterspell"] = true,
		["Improved Curse of Agony"] = true,
		["Improved Curse of Exhaustion"] = true,
		["Improved Curse of Weakness"] = true,
		["Improved Dampen Magic"] = true,
		["Improved Deadly Poison"] = true,
		["Improved Demoralizing Shout"] = true,
		["Improved Devotion Aura"] = true,
		["Improved Disarm"] = true,
		["Improved Distract"] = true,
		["Improved Drain Life"] = true,
		["Improved Drain Mana"] = true,
		["Improved Drain Soul"] = true,
		["Improved Enrage"] = true,
		["Improved Enslave Demon"] = true,
		["Improved Entangling Roots"] = true,
		["Improved Evasion"] = true,
		["Improved Eviscerate"] = true,
		["Improved Execute"] = true,
		["Improved Expose Armor"] = true,
		["Improved Eyes of the Beast"] = true,
		["Improved Fade"] = true,
		["Improved Feign Death"] = true,
		["Improved Fire Blast"] = true,
		["Improved Fire Nova Totem"] = true,
		["Improved Fire Ward"] = true,
		["Improved Fireball"] = true,
		["Improved Firebolt"] = true,
		["Improved Firestone"] = true,
		["Improved Flamestrike"] = true,
		["Improved Flametongue Weapon"] = true,
		["Improved Flash of Light"] = true,
		["Improved Frost Nova"] = true,
		["Improved Frost Ward"] = true,
		["Improved Frostbolt"] = true,
		["Improved Frostbrand Weapon"] = true,
		["Improved Garrote"] = true,
		["Improved Ghost Wolf"] = true,
		["Improved Gouge"] = true,
		["Improved Grace of Air Totem"] = true,
		["Improved Grounding Totem"] = true,
		["Improved Hammer of Justice"] = true,
		["Improved Hamstring"] = true,
		["Improved Healing Stream Totem"] = true,
		["Improved Healing Touch"] = true,
		["Improved Healing Wave"] = true,
		["Improved Healing"] = true,
		["Improved Health Funnel"] = true,
		["Improved Healthstone"] = true,
		["Improved Heroic Strike"] = true,
		["Improved Hunter's Mark"] = true,
		["Improved Immolate"] = true,
		["Improved Imp"] = true,
		["Improved Inner Fire"] = true,
		["Improved Instant Poison"] = true,
		["Improved Intercept"] = true,
		["Improved Intimidating Shout"] = true,
		["Improved Judgement"] = true,
		["Improved Kick"] = true,
		["Improved Kidney Shot"] = true,
		["Improved Lash of Pain"] = true,
		["Improved Lay on Hands"] = true,
		["Improved Lesser Healing Wave"] = true,
		["Improved Life Tap"] = true,
		["Improved Lightning Bolt"] = true,
		["Improved Lightning Shield"] = true,
		["Improved Magma Totem"] = true,
		["Improved Mana Burn"] = true,
		["Improved Mana Shield"] = true,
		["Improved Mana Spring Totem"] = true,
		["Improved Mark of the Wild"] = true,
		["Improved Mend Pet"] = true,
		["Improved Mind Blast"] = true,
		["Improved Moonfire"] = true,
		["Improved Nature's Grasp"] = true,
		["Improved Overpower"] = true,
		["Improved Power Word: Fortitude"] = true,
		["Improved Power Word: Shield"] = true,
		["Improved Prayer of Healing"] = true,
		["Improved Psychic Scream"] = true,
		["Improved Pummel"] = true,
		["Improved Regrowth"] = true,
		["Improved Reincarnation"] = true,
		["Improved Rejuvenation"] = true,
		["Improved Rend"] = true,
		["Improved Renew"] = true,
		["Improved Retribution Aura"] = true,
		["Improved Revenge"] = true,
		["Improved Revive Pet"] = true,
		["Improved Righteous Fury"] = true,
		["Improved Rockbiter Weapon"] = true,
		["Improved Rupture"] = true,
		["Improved Sap"] = true,
		["Improved Scorch"] = true,
		["Improved Scorpid Sting"] = true,
		["Improved Seal of Righteousness"] = true,
		["Improved Seal of the Crusader"] = true,
		["Improved Searing Pain"] = true,
		["Improved Searing Totem"] = true,
		["Improved Serpent Sting"] = true,
		["Improved Shadow Bolt"] = true,
		["Improved Shadow Word: Pain"] = true,
		["Improved Shield Bash"] = true,
		["Improved Shield Block"] = true,
		["Improved Shield Wall"] = true,
		["Improved Shred"] = true,
		["Improved Sinister Strike"] = true,
		["Improved Slam"] = true,
		["Improved Slice and Dice"] = true,
		["Improved Spellstone"] = true,
		["Improved Sprint"] = true,
		["Improved Starfire"] = true,
		["Improved Stoneclaw Totem"] = true,
		["Improved Stoneskin Totem"] = true,
		["Improved Strength of Earth Totem"] = true,
		["Improved Succubus"] = true,
		["Improved Sunder Armor"] = true,
		["Improved Taunt"] = true,
		["Improved Thorns"] = true,
		["Improved Thunder Clap"] = true,
		["Improved Tranquility"] = true,
		["Improved Vampiric Embrace"] = true,
		["Improved Vanish"] = true,
		["Improved Voidwalker"] = true,
		["Improved Windfury Weapon"] = true,
		["Improved Wing Clip"] = true,
		["Improved Wrath"] = true,
		["Incinerate"] = true,
		["Infected Bite"] = true,
		["Infected Wound"] = true,
		["Inferno Effect"] = true,
		["Inferno Shell"] = true,
		["Inferno"] = true,
		["Initiative"] = true,
		["Ink Spray"] = true,
		["Inner Fire"] = true,
		["Inner Focus"] = true,
		["Innervate"] = true,
		["Insect Swarm"] = true,
		["Inspiration"] = true,
		["Instant Poison II"] = true,
		["Instant Poison III"] = true,
		["Instant Poison IV"] = true,
		["Instant Poison V"] = true,
		["Instant Poison VI"] = true,
		["Instant Poison"] = true,
		["Intensity"] = true,
		["Intercept Stun"] = true,
		["Intercept"] = true,
		["Intervene"] = true,
		["Intimidating Roar"] = true,
		["Intimidating Shout"] = true,
		["Intimidation"] = true,
		["Intoxicating Venom"] = true,
		["Invisibility"] = true,
		["Invulnerability"] = true,
		["Iron Grenade"] = true,
		["Iron Will"] = true,
		["Jewelcrafting"] = true,
		["Judgement of Command"] = true,
		["Judgement of Justice"] = true,
		["Judgement of Light"] = true,
		["Judgement of Righteousness"] = true,
		["Judgement of the Crusader"] = true,
		["Judgement of Wisdom"] = true,
		["Judgement"] = true,
		["Kick - Silenced"] = true,
		["Kick"] = true,
		["Kidney Shot"] = true,
		["Kill Command"] = true,
		["Killer Instinct"] = true,
		["Knock Away"] = true,
		["Knockdown"] = true,
		["Kodo Riding"] = true,
		["Lacerate"] = true,
		["Large Copper Bomb"] = true,
		["Larva Goo"] = true,
		["Lash of Pain"] = true,
		["Lash"] = true,
		["Last Stand"] = true,
		["Lasting Judgement"] = true,
		["Lava Spout Totem"] = true,
		["Lay on Hands"] = true,
		["Leader of the Pack"] = true,
		["Leather"] = true,
		["Leatherworking"] = true,
		["Leech Poison"] = true,
		["Lesser Heal"] = true,
		["Lesser Healing Wave"] = true,
		["Lesser Invisibility"] = true,
		["Lethal Shots"] = true,
		["Lethality"] = true,
		["Levitate"] = true,
		["Libram"] = true,
		["Lich Slap"] = true,
		["Life Tap"] = true,
		["Lifebloom"] = true,
		["Lifegiving Gem"] = true,
		["Lightning Blast"] = true,
		["Lightning Bolt"] = true,
		["Lightning Breath"] = true,
		["Lightning Cloud"] = true,
		["Lightning Mastery"] = true,
		["Lightning Reflexes"] = true,
		["Lightning Shield"] = true,
		["Lightning Wave"] = true,
		["Lightwell Renew"] = true,
		["Lightwell"] = true,
		["Living Free Action"] = true,
		["Lizard Bolt"] = true,
		["Localized Toxin"] = true,
		["Lockpicking"] = true,
		["Long Daze"] = true,
		["Mace Specialization"] = true,
		["Mace Stun Effect"] = true,
		["Machine Gun"] = true,
		["Mage Armor"] = true,
		["Magic Attunement"] = true,
		["Magic Dust"] = true,
		["Magma Blast"] = true,
		["Magma Splash"] = true,
		["Magma Totem"] = true,
		["Mail"] = true,
		["Maim"] = true,
		["Malice"] = true,
		["Mana Burn"] = true,
		["Mana Feed"] = true,
		["Mana Shield"] = true,
		["Mana Spring Totem"] = true,
		["Mana Tide Totem"] = true,
		["Mangle (Bear)"] = true,
		["Mangle (Cat)"] = true,
		["Mangle"] = true,
		["Mark of Arlokk"] = true,
		["Mark of the Wild"] = true,
		["Martyrdom"] = true,
		["Mass Dispel"] = true,
		["Master Demonologist"] = true,
		["Master of Deception"] = true,
		["Master of Elements"] = true,
		["Master Summoner"] = true,
		["Maul"] = true,
		["Mechanostrider Piloting"] = true,
		["Meditation"] = true,
		["Megavolt"] = true,
		["Melee Specialization"] = true,
		["Melt Ore"] = true,
		["Mend Pet"] = true,
		["Mental Agility"] = true,
		["Mental Strength"] = true,
		["Mighty Blow"] = true,
		["Mind Blast"] = true,
		["Mind Control"] = true,
		["Mind Flay"] = true,
		["Mind Soothe"] = true,
		["Mind Tremor"] = true,
		["Mind Vision"] = true,
		["Mind-numbing Poison II"] = true,
		["Mind-numbing Poison III"] = true,
		["Mind-numbing Poison"] = true,
		["Mining"] = true,
		["Misdirection"] = true,
		["Mithril Frag Bomb"] = true,
		["Mocking Blow"] = true,
		["Molten Armor"] = true,
		["Molten Blast"] = true,
		["Molten Metal"] = true,
		["Mongoose Bite"] = true,
		["Monster Slaying"] = true,
		["Moonfire"] = true,
		["Moonfury"] = true,
		["Moonglow"] = true,
		["Moonkin Aura"] = true,
		["Moonkin Form"] = true,
		["Mortal Cleave"] = true,
		["Mortal Shots"] = true,
		["Mortal Strike"] = true,
		["Mortal Wound"] = true,
		["Multi-Shot"] = true,
		["Murder"] = true,
		["Mutilate"] = true,
		["Naralex's Nightmare"] = true,
		["Natural Armor"] = true,
		["Natural Shapeshifter"] = true,
		["Natural Weapons"] = true,
		["Nature Aligned"] = true,
		["Nature Resistance Totem"] = true,
		["Nature Resistance"] = true,
		["Nature Weakness"] = true,
		["Nature's Focus"] = true,
		["Nature's Grace"] = true,
		["Nature's Grasp"] = true,
		["Nature's Reach"] = true,
		["Nature's Swiftness"] = true,
		["Necrotic Poison"] = true,
		["Negative Charge"] = true,
		["Net"] = true,
		["Net-o-Matic"] = true,
		["Nightfall"] = true,
		["Nimble Reflexes"] = true,
		["Noxious Catalyst"] = true,
		["Noxious Cloud"] = true,
		["Omen of Clarity"] = true,
		["One-Handed Axes"] = true,
		["One-Handed Maces"] = true,
		["One-Handed Swords"] = true,
		["One-Handed Weapon Specialization"] = true,
		["Opening - No Text"] = true,
		["Opening"] = true,
		["Opportunity"] = true,
		["Overpower"] = true,
		["Pacify"] = true,
		["Pain Suppression"] = true,
		["Paralyzing Poison"] = true,
		["Paranoia"] = true,
		["Parasitic Serpent"] = true,
		["Parry"] = true,
		["Pathfinding"] = true,
		["Perception"] = true,
		["Permafrost"] = true,
		["Pet Aggression"] = true,
		["Pet Hardiness"] = true,
		["Pet Recovery"] = true,
		["Pet Resistance"] = true,
		["Petrify"] = true,
		["Phase Shift"] = true,
		["Pick Lock"] = true,
		["Pick Pocket"] = true,
		["Pierce Armor"] = true,
		["Piercing Howl"] = true,
		["Piercing Ice"] = true,
		["Piercing Shadow"] = true,
		["Piercing Shot"] = true,
		["Plague Cloud"] = true,
		["Plague Mind"] = true,
		["Plate Mail"] = true,
		["Poison Bolt Volley"] = true,
		["Poison Bolt"] = true,
		["Poison Cleansing Totem"] = true,
		["Poison Cloud"] = true,
		["Poison Shock"] = true,
		["Poison"] = true,
		["Poisoned Harpoon"] = true,
		["Poisoned Shot"] = true,
		["Poisonous Blood"] = true,
		["Poisons"] = true,
		["Polearm Specialization"] = true,
		["Polearms"] = true,
		["Polymorph"] = true,
		["Polymorph: Pig"] = true,
		["Polymorph: Turtle"] = true,
		["Portal: Darnassus"] = true,
		["Portal: Ironforge"] = true,
		["Portal: Orgrimmar"] = true,
		["Portal: Stormwind"] = true,
		["Portal: Thunder Bluff"] = true,
		["Portal: Undercity"] = true,
		["Positive Charge"] = true,
		["Pounce Bleed"] = true,
		["Pounce"] = true,
		["Power Infusion"] = true,
		["Power Word: Fortitude"] = true,
		["Power Word: Shield"] = true,
		["Prayer Beads Blessing"] = true,
		["Prayer of Fortitude"] = true,
		["Prayer of Healing"] = true,
		["Prayer of Mending"] = true,
		["Prayer of Shadow Protection"] = true,
		["Prayer of Spirit"] = true,
		["Precision"] = true,
		["Predatory Strikes"] = true,
		["Premeditation"] = true,
		["Preparation"] = true,
		["Presence of Mind"] = true,
		["Primal Fury"] = true,
		["Prowl"] = true,
		["Psychic Scream"] = true,
		["Pummel"] = true,
		["Puncture"] = true,
		["Purge"] = true,
		["Purification"] = true,
		["Purify"] = true,
		["Pursuit of Justice"] = true,
		["Putrid Breath"] = true,
		["Putrid Enzyme"] = true,
		["Pyroblast"] = true,
		["Pyroclasm"] = true,
		["Quick Flame Ward"] = true,
		["Quick Shots"] = true,
		["Quickness"] = true,
		["Radiation Bolt"] = true,
		["Radiation Cloud"] = true,
		["Radiation Poisoning"] = true,
		["Radiation"] = true,
		["Rain of Fire"] = true,
		["Rake"] = true,
		["Ram Riding"] = true,
		["Rampage"] = true,
		["Ranged Weapon Specialization"] = true,
		["Rapid Concealment"] = true,
		["Rapid Fire"] = true,
		["Raptor Riding"] = true,
		["Raptor Strike"] = true,
		["Ravage"] = true,
		["Ravenous Claw"] = true,
		["Readiness"] = true,
		["Rebirth"] = true,
		["Rebuild"] = true,
		["Recently Bandaged"] = true,
		["Reckless Charge"] = true,
		["Recklessness"] = true,
		["Reckoning"] = true,
		["Recombobulate"] = true,
		["Redemption"] = true,
		["Redoubt"] = true,
		["Reflection"] = true,
		["Regeneration"] = true,
		["Regrowth"] = true,
		["Reincarnation"] = true,
		["Rejuvenation"] = true,
		["Relentless Strikes"] = true,
		["Remorseless Attacks"] = true,
		["Remorseless"] = true,
		["Remove Curse"] = true,
		["Remove Insignia"] = true,
		["Remove Lesser Curse"] = true,
		["Rend"] = true,
		["Renew"] = true,
		["Repentance"] = true,
		["Repulsive Gaze"] = true,
		["Restorative Totems"] = true,
		["Resurrection"] = true,
		["Retaliation"] = true,
		["Retribution Aura"] = true,
		["Revenge Stun"] = true,
		["Revenge"] = true,
		["Reverberation"] = true,
		["Revive Pet"] = true,
		["Rhahk'Zor Slam"] = true,
		["Ribbon of Souls"] = true,
		["Righteous Defense"] = true,
		["Righteous Fury"] = true,
		["Rip"] = true,
		["Riposte"] = true,
		["Ritual of Doom Effect"] = true,
		["Ritual of Doom"] = true,
		["Ritual of Souls"] = true,
		["Ritual of Summoning"] = true,
		["Rockbiter Weapon"] = true,
		["Rogue Passive"] = true,
		["Rough Copper Bomb"] = true,
		["Rough Sharpening Stone"] = true,
		["Ruin"] = true,
		["Rupture"] = true,
		["Ruthlessness"] = true,
		["Sacrifice"] = true,
		["Safe Fall"] = true,
		["Sanctity Aura"] = true,
		["Sap"] = true,
		["Savage Fury"] = true,
		["Savage Strikes"] = true,
		["Scare Beast"] = true,
		["Scatter Shot"] = true,
		["Scorch"] = true,
		["Scorpid Poison"] = true,
		["Scorpid Sting"] = true,
		["Screams of the Past"] = true,
		["Screech"] = true,
		["Seal Fate"] = true,
		["Seal of Blood"] = true,
		["Seal of Command"] = true,
		["Seal of Justice"] = true,
		["Seal of Light"] = true,
		["Seal of Reckoning"] = true,
		["Seal of Righteousness"] = true,
		["Seal of the Crusader"] = true,
		["Seal of Vengeance"] = true,
		["Seal of Wisdom"] = true,
		["Searing Light"] = true,
		["Searing Pain"] = true,
		["Searing Totem"] = true,
		["Second Wind"] = true,
		["Seduction"] = true,
		["Seed of Corruption"] = true,
		["Sense Demons"] = true,
		["Sense Undead"] = true,
		["Sentry Totem"] = true,
		["Serpent Sting"] = true,
		["Setup"] = true,
		["Shackle Undead"] = true,
		["Shadow Affinity"] = true,
		["Shadow Bolt Volley"] = true,
		["Shadow Bolt"] = true,
		["Shadow Flame"] = true,
		["Shadow Focus"] = true,
		["Shadow Mastery"] = true,
		["Shadow Protection"] = true,
		["Shadow Reach"] = true,
		["Shadow Reflector"] = true,
		["Shadow Resistance Aura"] = true,
		["Shadow Resistance"] = true,
		["Shadow Shock"] = true,
		["Shadow Trance"] = true,
		["Shadow Vulnerability"] = true,
		["Shadow Ward"] = true,
		["Shadow Weakness"] = true,
		["Shadow Weaving"] = true,
		["Shadow Word: Death"] = true,
		["Shadow Word: Pain"] = true,
		["Shadowburn"] = true,
		["Shadowfiend"] = true,
		["Shadowform"] = true,
		["Shadowfury"] = true,
		["Shadowguard"] = true,
		["Shadowmeld Passive"] = true,
		["Shadowmeld"] = true,
		["Shadowstep"] = true,
		["Shamanistic Rage"] = true,
		["Sharpened Claws"] = true,
		["Shatter"] = true,
		["Sheep"] = true,
		["Shell Shield"] = true,
		["Shield Bash - Silenced"] = true,
		["Shield Bash"] = true,
		["Shield Block"] = true,
		["Shield Slam"] = true,
		["Shield Specialization"] = true,
		["Shield Wall"] = true,
		["Shield"] = true,
		["Shiv"] = true,
		["Shock"] = true,
		["Shoot Bow"] = true,
		["Shoot Crossbow"] = true,
		["Shoot Gun"] = true,
		["Shoot"] = true,
		["Shred"] = true,
		["Shrink"] = true,
		["Silence"] = true,
		["Silencing Shot"] = true,
		["Silent Resolve"] = true,
		["Silithid Pox"] = true,
		["Sinister Strike"] = true,
		["Siphon Life"] = true,
		["Skinning"] = true,
		["Skull Crack"] = true,
		["Slam"] = true,
		["Sleep"] = true,
		["Slice and Dice"] = true,
		["Slow Fall"] = true,
		["Slow"] = true,
		["Slowing Poison"] = true,
		["Small Bronze Bomb"] = true,
		["Smelting"] = true,
		["Smite Slam"] = true,
		["Smite Stomp"] = true,
		["Smite"] = true,
		["Smoke Bomb"] = true,
		["Snake Trap"] = true,
		["Snap Kick"] = true,
		["Solid Sharpening Stone"] = true,
		["Sonic Burst"] = true,
		["Soothe Animal"] = true,
		["Soothing Kiss"] = true,
		["Soul Bite"] = true,
		["Soul Drain"] = true,
		["Soul Fire"] = true,
		["Soul Link"] = true,
		["Soul Siphon"] = true,
		["Soul Tap"] = true,
		["Soulshatter"] = true,
		["Soulstone Resurrection"] = true,
		["Spell Lock"] = true,
		["Spell Reflection"] = true,
		["Spell Warding"] = true,
		["Spellsteal"] = true,
		["Spirit Bond"] = true,
		["Spirit Burst"] = true,
		["Spirit of Redemption"] = true,
		["Spirit Tap"] = true,
		["Spiritual Attunement"] = true,
		["Spiritual Focus"] = true,
		["Spiritual Guidance"] = true,
		["Spiritual Healing"] = true,
		["Spit"] = true,
		["Spore Cloud"] = true,
		["Sprint"] = true,
		["Stance Mastery"] = true,
		["Starfire Stun"] = true,
		["Starfire"] = true,
		["Starshards"] = true,
		["Staves"] = true,
		["Steady Shot"] = true,
		["Stealth"] = true,
		["Stoneclaw Totem"] = true,
		["Stoneform"] = true,
		["Stoneskin Totem"] = true,
		["Stormstrike"] = true,
		["Strength of Earth Totem"] = true,
		["Strike"] = true,
		["Stuck"] = true,
		["Stun"] = true,
		["Subtlety"] = true,
		["Suffering"] = true,
		["Summon Charger"] = true,
		["Summon Dreadsteed"] = true,
		["Summon Felguard"] = true,
		["Summon Felhunter"] = true,
		["Summon Felsteed"] = true,
		["Summon Imp"] = true,
		["Summon Ragnaros"] = true,
		["Summon Spawn of Bael'Gar"] = true,
		["Summon Succubus"] = true,
		["Summon Voidwalker"] = true,
		["Summon Warhorse"] = true,
		["Summon Water Elemental"] = true,
		["Sunder Armor"] = true,
		["Suppression"] = true,
		["Surefooted"] = true,
		["Survivalist"] = true,
		["Sweeping Slam"] = true,
		["Sweeping Strikes"] = true,
		["Swiftmend"] = true,
		["Swipe"] = true,
		["Swoop"] = true,
		["Sword Specialization"] = true,
		["Tactical Mastery"] = true,
		["Tailoring"] = true,
		["Tainted Blood"] = true,
		["Tame Beast"] = true,
		["Tamed Pet Passive"] = true,
		["Taunt"] = true,
		["Teleport: Darnassus"] = true,
		["Teleport: Ironforge"] = true,
		["Teleport: Moonglade"] = true,
		["Teleport: Orgrimmar"] = true,
		["Teleport: Stormwind"] = true,
		["Teleport: Thunder Bluff"] = true,
		["Teleport: Undercity"] = true,
		["Tendon Rip"] = true,
		["Tendon Slice"] = true,
		["Terrify"] = true,
		["Terrifying Screech"] = true,
		["The Beast Within"] = true,
		["The Eye of the Dead"] = true,
		["The Furious Storm"] = true,
		["The Human Spirit"] = true,
		["Thick Hide"] = true,
		["Thorium Grenade"] = true,
		["Thorn Volley"] = true,
		["Thorns"] = true,
		["Thousand Blades"] = true,
		["Threatening Gaze"] = true,
		["Throw Axe"] = true,
		["Throw Dynamite"] = true,
		["Throw Liquid Fire"] = true,
		["Throw Wrench"] = true,
		["Throw"] = true,
		["Throwing Specialization"] = true,
		["Throwing Weapon Specialization"] = true,
		["Thrown"] = true,
		["Thunder Clap"] = true,
		["Thunderclap"] = true,
		["Thunderfury"] = true,
		["Thundering Strikes"] = true,
		["Thundershock"] = true,
		["Thunderstomp"] = true,
		["Tidal Charm"] = true,
		["Tidal Focus"] = true,
		["Tidal Mastery"] = true,
		["Tiger Riding"] = true,
		["Tiger's Fury"] = true,
		["Torment"] = true,
		["Totem of Wrath"] = true,
		["Totem"] = true,
		["Totemic Focus"] = true,
		["Touch of Weakness"] = true,
		["Toughness"] = true,
		["Toxic Saliva"] = true,
		["Toxic Spit"] = true,
		["Toxic Volley"] = true,
		["Traces of Silithyst"] = true,
		["Track Beasts"] = true,
		["Track Demons"] = true,
		["Track Dragonkin"] = true,
		["Track Elementals"] = true,
		["Track Giants"] = true,
		["Track Hidden"] = true,
		["Track Humanoids"] = true,
		["Track Undead"] = true,
		["Trample"] = true,
		["Tranquil Air Totem"] = true,
		["Tranquil Spirit"] = true,
		["Tranquility"] = true,
		["Tranquilizing Poison"] = true,
		["Tranquilizing Shot"] = true,
		["Trap Mastery"] = true,
		["Travel Form"] = true,
		["Tree of Life"] = true,
		["Trelane's Freezing Touch"] = true,
		["Tremor Totem"] = true,
		["Tribal Leatherworking"] = true,
		["Trueshot Aura"] = true,
		["Turn Undead"] = true,
		["Twisted Tranquility"] = true,
		["Two-Handed Axes and Maces"] = true,
		["Two-Handed Axes"] = true,
		["Two-Handed Maces"] = true,
		["Two-Handed Swords"] = true,
		["Two-Handed Weapon Specialization"] = true,
		["Ultrasafe Transporter: Gadgetzan"] = true,
		["Unarmed"] = true,
		["Unbreakable Will"] = true,
		["Unbridled Wrath Effect"] = true,
		["Unbridled Wrath"] = true,
		["Undead Horsemanship"] = true,
		["Underwater Breathing"] = true,
		["Unending Breath"] = true,
		["Unholy Frenzy"] = true,
		["Unholy Power"] = true,
		["Unleashed Fury"] = true,
		["Unleashed Rage"] = true,
		["Unstable Affliction"] = true,
		["Unstable Concoction"] = true,
		["Unstable Power"] = true,
		["Unyielding Faith"] = true,
		["Uppercut"] = true,
		["Vampiric Embrace"] = true,
		["Vampiric Touch"] = true,
		["Vanish"] = true,
		["Vanished"] = true,
		["Veil of Shadow"] = true,
		["Vengeance"] = true,
		["Venom Spit"] = true,
		["Venom Sting"] = true,
		["Venomhide Poison"] = true,
		["Vicious Rend"] = true,
		["Victory Rush"] = true,
		["Vigor"] = true,
		["Vile Poisons"] = true,
		["Vindication"] = true,
		["Viper Sting"] = true,
		["Virulent Poison"] = true,
		["Void Bolt"] = true,
		["Volley"] = true,
		["Walking Bomb Effect"] = true,
		["Wand Specialization"] = true,
		["Wandering Plague"] = true,
		["Wands"] = true,
		["War Stomp"] = true,
		["Ward of the Eye"] = true,
		["Water Breathing"] = true,
		["Water Shield"] = true,
		["Water Walking"] = true,
		["Water"] = true,
		["Waterbolt"] = true,
		["Wavering Will"] = true,
		["Weak Frostbolt"] = true,
		["Weakened Soul"] = true,
		["Weaponsmith"] = true,
		["Web Explosion"] = true,
		["Web Spin"] = true,
		["Web Spray"] = true,
		["Web"] = true,
		["Whirling Barrage"] = true,
		["Whirling Trip"] = true,
		["Whirlwind"] = true,
		["Wide Slash"] = true,
		["Will of Hakkar"] = true,
		["Will of the Forsaken"] = true,
		["Windfury Totem"] = true,
		["Windfury Weapon"] = true,
		["Windsor's Frenzy"] = true,
		["Windwall Totem"] = true,
		["Wing Buffet"] = true,
		["Wing Clip"] = true,
		["Wing Flap"] = true,
		["Winter's Chill"] = true,
		["Wisp Spirit"] = true,
		["Wither Touch"] = true,
		["Wolf Riding"] = true,
		["Wound Poison II"] = true,
		["Wound Poison III"] = true,
		["Wound Poison IV"] = true,
		["Wound Poison"] = true,
		["Wrath of Air Totem"] = true,
		["Wrath"] = true,
		["Wyvern Sting"] = true,
	}

	local spellIcons = {
		["Free Action"] = "Inv_potion_04",
		["Fire Reflector"] = "spell_fire_sealoffire",
		["Net-o-Matic"] = "ability_ensnare",
		["Shadow Reflector"] = "spell_shadow_antishadow",
		["Nimble Reflexes"] = "ability_meleedamage",
		["Damage Absorb"] = "spell_holy_devotionaura",
		["Frost Reflector"] = "spell_frost_frostward",
		["Living Free Action"] = "Inv_potion_07",
		["Earthbind"] = "spell_nature_strengthofearthtotem02",
		["Boar Charge"] = "Ability_hunter_pet_boar",
		["Grounding Totem Effect"] = "Spell_nature_groundingtotem",
		["Invulnerability"] = "Spell_holy_divineintervention",
		["Ward of the Eye"] = "spell_totem_wardofdraining",
		["Flee"] = "spell_magic_polymorphchicken",
		
		["Abolish Disease"] = "Spell_Nature_NullifyDisease",
		["Abolish Poison Effect"] = "Spell_Nature_NullifyPoison_02",
		["Abolish Poison"] = "Spell_Nature_NullifyPoison_02",
		["Acid Breath"] = "Spell_Nature_Acid_01",
		["Acid of Hakkar"] = "Spell_Nature_Acid_01",
		["Acid Spit"] = "Spell_Nature_CorrosiveBreath",
		["Acid Splash"] = "INV_Drink_06",
		["Activate MG Turret"] = "INV_Weapon_Rifle_10",
		["Adrenaline Rush"] = "Spell_Shadow_ShadowWordDominate",
		["Aftermath"] = "Spell_Fire_Fire",
		["Aggression"] = "Ability_Racial_Avatar",
		["Aimed Shot"] = "INV_Spear_07",
		["Alchemy"] = "Trade_Alchemy",
		["Ambush"] = "Ability_Rogue_Ambush",
		["Amplify Curse"] = "Spell_Shadow_Contagion",
		["Amplify Damage"] = "Spell_Nature_AbolishMagic",
		["Amplify Flames"] = "Spell_Fire_Fireball",
		["Amplify Magic"] = "Spell_Holy_FlashHeal",
		["Ancestral Fortitude"] = "Spell_Nature_UndyingStrength",
		["Ancestral Healing"] = "Spell_Nature_UndyingStrength",
		["Ancestral Knowledge"] = "Spell_Shadow_GrimWard",
		["Ancestral Spirit"] = "Spell_Nature_Regenerate",
		["Anger Management"] = "Spell_Holy_BlessingOfStamina",
		["Anticipation"] = "Spell_Nature_MirrorImage",
		["Aqua Jet"] = "Spell_Frost_ChillingBlast",
		["Aquatic Form"] = "Ability_Druid_AquaticForm",
		["Arcane Bolt"] = "Spell_Arcane_StarFire",
		["Arcane Bomb"] = "Spell_shadow_mindbomb",
		["Arcane Brilliance"] = "Spell_Holy_ArcaneIntellect",
		["Arcane Concentration"] = "Spell_Shadow_ManaBurn",
		["Arcane Explosion"] = "Spell_Nature_WispSplode",
		["Arcane Focus"] = "Spell_Holy_Devotion",
		["Arcane Instability"] = "Spell_Shadow_Teleport",
		["Arcane Intellect"] = "Spell_Holy_MagicalSentry",
		["Arcane Meditation"] = "Spell_Shadow_SiphonMana",
		["Arcane Mind"] = "Spell_Shadow_Charm",
		["Arcane Missile"] = "Spell_Nature_StarFall",
		["Arcane Missiles"] = "Spell_Nature_StarFall",
		["Arcane Potency"] = "Spell_Arcane_StarFire",
		["Arcane Power"] = "Spell_Nature_Lightning",
		["Arcane Resistance"] = "Spell_Nature_StarFall",
		["Arcane Shot"] = "Ability_ImpalingBolt",
		["Arcane Subtlety"] = "Spell_Holy_DispelMagic",
		["Arcane Weakness"] = "INV_Misc_QirajiCrystal_01",
		["Arcing Smash"] = "Ability_Warrior_Cleave",
		["Arctic Reach"] = "Spell_Shadow_DarkRitual",
		["Arugal's Curse"] = "Spell_Shadow_GatherShadows",
		["Arugal's Gift"] = "Spell_Shadow_ChillTouch",
		["Ascendance"] = "INV_Misc_Gem_Pearl_04",
		["Aspect of Arlokk"] = "Ability_Vanish",
		["Aspect of Jeklik"] = "Spell_Shadow_Teleport",
		["Aspect of Mar'li"] = "Ability_Smash",
		["Aspect of the Beast"] = "Ability_Mount_PinkTiger",
		["Aspect of the Cheetah"] = "Ability_Mount_JungleTiger",
		["Aspect of the Hawk"] = "Spell_Nature_RavenForm",
		["Aspect of the Monkey"] = "Ability_Hunter_AspectOfTheMonkey",
		["Aspect of the Pack"] = "Ability_Mount_WhiteTiger",
		["Aspect of the Wild"] = "Spell_Nature_ProtectionformNature",
		["Aspect of Venoxis"] = "Spell_Nature_CorrosiveBreath",
		["Astral Recall"] = "Spell_Nature_AstralRecal",
		["Attack"] = "Spell_Fire_SearingTotem",
		["Attacking"] = "Temp",
		["Aura of Command"] = "INV_Banner_03",
		["Aural Shock"] = "Spell_Shadow_Possession",
		["Auto Shot"] = "Ability_Whirlwind",
		["Avoidance"] = "Ability_Warrior_Revenge",
		["Axe Flurry"] = "INV_Axe_06",
		["Axe Specialization"] = "INV_Axe_06",
		["Axe Toss"] = "INV_Axe_04",
		["Backhand"] = "INV_Gauntlets_05",
		["Backstab"] = "Ability_BackStab",
		["Bane"] = "Spell_Shadow_DeathPact",
		["Baneful Poison"] = "Spell_Nature_CorrosiveBreath",
		["Banish"] = "Spell_Shadow_Cripple",
		["Banshee Curse"] = "Spell_Nature_Drowsy",
		["Banshee Shriek"] = "Spell_Shadow_ImpPhaseShift",
		["Banshee Wail"] = "Spell_shadow_shadowbolt",
		["Barbed Sting"] = "Spell_Nature_NullifyPoison",
		["Barkskin Effect"] = "Spell_Nature_StoneClawTotem",
		["Barkskin"] = "Spell_Nature_StoneClawTotem",
		["Barrage"] = "Ability_UpgradeMoonGlaive",
		["Bash"] = "Ability_Druid_Bash",
		["Basic Campfire"] = "Spell_Fire_Fire",
		["Battle Shout"] = "Ability_Warrior_BattleShout",
		["Battle Stance Passive"] = "Ability_Warrior_OffensiveStance",
		["Battle Stance"] = "Ability_Warrior_OffensiveStance",
		["Bear Form"] = "Ability_Racial_BearForm",
		["Beast Lore"] = "Ability_Physical_Taunt",
		["Beast Slaying"] = "INV_Misc_Pelt_Bear_Ruin_02",
		["Beast Training"] = "Ability_Hunter_BeastCall02",
		["Befuddlement"] = "Spell_Shadow_MindSteal",
		["Bellowing Roar"] = "Spell_fire_fire",
		["Benediction"] = "Spell_Frost_WindWalkOn",
		["Berserker Charge"] = "Ability_Warrior_Charge",
		["Berserker Rage"] = "Spell_Nature_AncestralGuardian",
		["Berserker Stance Passive"] = "Ability_Racial_Avatar",
		["Berserker Stance"] = "Ability_Racial_Avatar",
		["Berserking"] = "Racial_Troll_Berserk",
		["Bestial Discipline"] = "Spell_Nature_AbolishMagic",
		["Bestial Swiftness"] = "Ability_Druid_Dash",
		["Bestial Wrath"] = "Ability_Druid_FerociousBite",
		["Big Bronze Bomb"] = "Inv_misc_bomb_05",
		["Big Iron Bomb"] = "Inv_misc_bomb_01",
		["Biletoad Infection"] = "Spell_Holy_HarmUndeadAura",
		["Bite"] = "Ability_Racial_Cannibalize",
		["Black Arrow"] = "Ability_TheBlackArrow",
		["Black Sludge"] = "spell_shadow_callofbone",
		["Blackout"] = "Spell_Shadow_GatherShadows",
		["Blacksmithing"] = "Trade_BlackSmithing",
		["Blade Flurry"] = "Ability_Warrior_PunishingBlow",
		["Blast Wave"] = "Spell_Holy_Excorcism_02",
		["Blaze"] = "Spell_Fire_Incinerate",
		["Blessed Recovery"] = "Spell_Holy_BlessedRecovery",
		["Blessing of Blackfathom"] = "Spell_Frost_FrostWard",
		["Blessing of Freedom"] = "Spell_Holy_SealOfValor",
		["Blessing of Kings"] = "Spell_Magic_MageArmor",
		["Blessing of Light"] = "Spell_Holy_PrayerOfHealing02",
		["Blessing of Might"] = "Spell_Holy_FistOfJustice",
		["Blessing of Protection"] = "Spell_Holy_SealOfProtection",
		["Blessing of Sacrifice"] = "Spell_Holy_SealOfSacrifice",
		["Blessing of Salvation"] = "Spell_Holy_SealOfSalvation",
		["Blessing of Sanctuary"] = "Spell_Nature_LightningShield",
		["Blessing of Shahram"] = "Spell_Holy_LayOnHands",
		["Blessing of Wisdom"] = "Spell_Holy_SealOfWisdom",
		["Blind"] = "Spell_Shadow_MindSteal",
		["Blinding Powder"] = "INV_Misc_Ammo_Gunpowder_02",
		["Blink"] = "Spell_Arcane_Blink",
		["Blizzard"] = "Spell_Frost_IceStorm",
		["Block"] = "Ability_Defend",
		["Blood Craze"] = "Spell_Shadow_SummonImp",
		["Blood Frenzy"] = "Ability_GhoulFrenzy",
		["Blood Funnel"] = "Spell_Shadow_LifeDrain",
		["Blood Fury"] = "Racial_Orc_BerserkerStrength",
		["Blood Leech"] = "Ability_Racial_Cannibalize",
		["Blood Pact"] = "Spell_Shadow_BloodBoil",
		["Blood Siphon"] = "Spell_Shadow_LifeDrain",
		["Blood Tap"] = "Ability_Racial_Cannibalize",
		["Bloodlust"] = "Spell_Nature_Bloodlust",
		["Bloodrage"] = "Ability_Racial_BloodRage",
		["Bloodthirst"] = "Spell_Nature_BloodLust",
		["Bomb"] = "Spell_Fire_SelfDestruct",
		["Booming Voice"] = "Spell_Nature_Purge",
		["Boulder"] = "INV_Stone_14",
		["Bow Specialization"] = "INV_Weapon_Bow_12",
		["Bows"] = "INV_Weapon_Bow_05",
		["Brain Wash"] = "Spell_Shadow_AntiMagicShell",
		["Breath"] = "Spell_fire_fire",
		["Bright Campfire"] = "Spell_Fire_Fire",
		["Brutal Impact"] = "Ability_Druid_Bash",
		["Burning Adrenaline"] = "INV_Gauntlets_03",
		["Burning Soul"] = "Spell_Fire_Fire",
		["Burning Wish"] = "Spell_Shadow_PsychicScream",
		["Butcher Drain"] = "Spell_Shadow_SiphonMana",
		["Call of Flame"] = "Spell_Fire_Immolation",
		["Call of the Grave"] = "Spell_Shadow_ChillTouch",
		["Call of Thunder"] = "Spell_Nature_CallStorm",
		["Call Pet"] = "Ability_Hunter_BeastCall",
		["Camouflage"] = "Ability_Stealth",
		["Cannibalize"] = "Ability_Racial_Cannibalize",
		["Cat Form"] = "Ability_Druid_CatForm",
		["Cataclysm"] = "Spell_Fire_WindsofWoe",
		["Cause Insanity"] = "Spell_Shadow_ShadowWordDominate",
		["Chain Bolt"] = "Spell_Nature_ChainLightning",
		["Chain Burn"] = "Spell_Shadow_ManaBurn",
		["Chain Heal"] = "Spell_Nature_HealingWaveGreater",
		["Chain Lightning"] = "Spell_Nature_ChainLightning",
		["Chained Bolt"] = "Spell_Nature_ChainLightning",
		["Chains of Ice"] = "Spell_Frost_ChainsOfIce",
		["Challenging Roar"] = "Ability_Druid_ChallangingRoar",
		["Challenging Shout"] = "Ability_BullRush",
		["Charge Rage Bonus Effect"] = "Ability_Warrior_Charge",
		["Charge Stun"] = "Spell_Frost_Stun",
		["Charge"] = "Ability_Warrior_Charge",
		["Cheap Shot"] = "Ability_CheapShot",
		["Chilled"] = "Spell_Frost_FrostArmor02",
		["Chilling Touch"] = "Spell_Frost_FrostArmor",
		["Chromatic Infusion"] = "Spell_Holy_MindVision",
		["Claw"] = "Ability_Druid_Rake",
		["Cleanse Nova"] = "Spell_Holy_HolyBolt",
		["Cleanse"] = "Spell_Holy_Renew",
		["Clearcasting"] = "Spell_Shadow_ManaBurn",
		["Cleave"] = "Ability_Warrior_Cleave",
		["Clever Traps"] = "Spell_Nature_TimeStop",
		["Clone"] = "spell_shadow_blackplague",
		["Closing"] = "Temp",
		["Cloth"] = "INV_Chest_Cloth_21",
		["Cobra Reflexes"] = "Spell_Nature_GuardianWard",
		["Cold Blood"] = "Spell_Ice_Lament",
		["Cold Snap"] = "Spell_Frost_WizardMark",
		["Combat Endurance"] = "Spell_Nature_AncestralGuardian",
		["Combustion"] = "Spell_Fire_SealOfFire",
		["Command"] = "Ability_Warrior_WarCry",
		["Commanding Shout"] = "Spell_Magic_Magearmor",
		["Concentration Aura"] = "Spell_Holy_MindSooth",
		["Concussion Blow"] = "Ability_ThunderBolt",
		["Concussion"] = "Spell_Fire_Fireball",
		["Concussive Shot"] = "Spell_Frost_Stun",
		["Cone of Cold"] = "Spell_Frost_Glacier",
		["Conflagrate"] = "Spell_Fire_Fireball",
		["Conjure Food"] = "INV_Misc_Food_10",
		["Conjure Mana Agate"] = "INV_Misc_Gem_Emerald_01",
		["Conjure Mana Citrine"] = "INV_Misc_Gem_Opal_01",
		["Conjure Mana Jade"] = "INV_Misc_Gem_Emerald_02",
		["Conjure Mana Ruby"] = "INV_Misc_Gem_Ruby_01",
		["Conjure Water"] = "INV_Drink_06",
		["Consecration"] = "Spell_Holy_InnerFire",
		["Consume Shadows"] = "Spell_Shadow_AntiShadow",
		["Consuming Shadows"] = "Spell_Shadow_Haunting",
		["Convection"] = "Spell_Nature_WispSplode",
		["Conviction"] = "Spell_Holy_RetributionAura",
		["Cooking"] = "INV_Misc_Food_15",
		["Corrosive Acid Breath"] = "Spell_Nature_Acid_01",
		["Corrosive Ooze"] = "Spell_Shadow_AnimateDead",
		["Corrosive Poison"] = "Spell_Nature_CorrosiveBreath",
		["Corrupted Blood"] = "Spell_Shadow_CorpseExplode",
		["Corruption"] = "Spell_Shadow_AbominationExplosion",
		["Counterattack"] = "Ability_Warrior_Challange",
		["Counterspell - Silenced"] = "Spell_Frost_IceShock",
		["Counterspell"] = "Spell_Frost_IceShock",
		["Cower"] = "Ability_Druid_Cower",
		["Create Firestone (Greater)"] = "INV_Ammo_FireTar",
		["Create Firestone (Lesser)"] = "INV_Ammo_FireTar",
		["Create Firestone (Major)"] = "INV_Ammo_FireTar",
		["Create Firestone"] = "INV_Ammo_FireTar",
		["Create Healthstone (Greater)"] = "INV_Stone_04",
		["Create Healthstone (Lesser)"] = "INV_Stone_04",
		["Create Healthstone (Major)"] = "INV_Stone_04",
		["Create Healthstone (Minor)"] = "INV_Stone_04",
		["Create Healthstone"] = "INV_Stone_04",
		["Create Soulstone (Greater)"] = "Spell_Shadow_SoulGem",
		["Create Soulstone (Lesser)"] = "Spell_Shadow_SoulGem",
		["Create Soulstone (Major)"] = "Spell_Shadow_SoulGem",
		["Create Soulstone (Minor)"] = "Spell_Shadow_SoulGem",
		["Create Soulstone"] = "Spell_Shadow_SoulGem",
		["Create Spellstone (Greater)"] = "INV_Misc_Gem_Sapphire_01",
		["Create Spellstone (Major)"] = "INV_Misc_Gem_Sapphire_01",
		["Create Spellstone"] = "INV_Misc_Gem_Sapphire_01",
		["Creeper Venom"] = "Spell_Nature_NullifyPoison",
		["Creeping Mold"] = "spell_shadow_creepingplague",
		["Cripple"] = "Spell_Shadow_Cripple",
		["Crippling Poison II"] = "Ability_PoisonSting",
		["Crippling Poison"] = "Ability_PoisonSting",
		["Critical Mass"] = "Spell_Nature_WispHeal",
		["Crossbows"] = "INV_Weapon_Crossbow_01",
		["Crowd Pummel"] = "INV_Gauntlets_04",
		["Cruelty"] = "Ability_Rogue_Eviscerate",
		["Crusader Strike"] = "Spell_Holy_HolySmite",
		["Crusader's Wrath"] = "Spell_Nature_GroundingTotem",
		["Crystal Charge"] = "INV_Misc_Gem_Opal_01",
		["Crystal Flash"] = "spell_shadow_teleport",
		["Crystal Force"] = "INV_Misc_Gem_Crystal_02",
		["Crystal Gaze"] = "ability_golemthunderclap",
		["Crystal Restore"] = "INV_Misc_Gem_Diamond_02",
		["Crystal Spire"] = "INV_Misc_Gem_Stone_01",
		["Crystal Ward"] = "INV_Misc_Gem_Ruby_02",
		["Crystal Yield"] = "INV_Misc_Gem_Amethyst_01",
		["Crystalline Slumber"] = "Spell_Nature_Sleep",
		["Cultivate Packet of Seeds"] = "inv_misc_food_45",
		["Cultivation"] = "INV_Misc_Flower_01",
		["Cure Disease"] = "Spell_Holy_NullifyDisease",
		["Cure Poison"] = "Spell_Nature_NullifyPoison",
		["Curse of Agony"] = "Spell_Shadow_CurseOfSargeras",
		["Curse of Blood"] = "Spell_Shadow_RitualOfSacrifice",
		["Curse of Doom Effect"] = "Spell_Shadow_AuraOfDarkness",
		["Curse of Doom"] = "Spell_Shadow_AuraOfDarkness",
		["Curse of Exhaustion"] = "Spell_Shadow_GrimWard",
		["Curse of Idiocy"] = "Spell_Shadow_MindRot",
		["Curse of Mending"] = "spell_shadow_antishadow",
		["Curse of Recklessness"] = "Spell_Shadow_UnholyStrength",
		["Curse of Shadow"] = "Spell_Shadow_CurseOfAchimonde",
		["Curse of the Deadwood"] = "Spell_Shadow_GatherShadows",
		["Curse of the Elemental Lord"] = "Spell_Fire_LavaSpawn",
		["Curse of the Elements"] = "Spell_Shadow_ChillTouch",
		["Curse of the Fallen Magram"] = "spell_shadow_unholyfrenzy",
		["Curse of Tongues"] = "Spell_Shadow_CurseOfTounges",
		["Curse of Tuten'kash"] = "Spell_Nature_Drowsy",
		["Curse of Weakness"] = "Spell_Shadow_CurseOfMannoroth",
		["Cursed Blood"] = "Spell_Nature_Drowsy",
		["Cyclone"] = "Spell_Nature_Cyclone",
		["Dagger Specialization"] = "INV_Weapon_ShortBlade_05",
		["Daggers"] = "Ability_SteelMelee",
		["Dampen Magic"] = "Spell_Nature_AbolishMagic",
		["Dark Iron Bomb"] = "Spell_Fire_SelfDestruct",
		["Dark Mending"] = "Spell_shadow_chilltouch",
		["Dark Offering"] = "Spell_Shadow_Haunting",
		["Dark Pact"] = "Spell_Shadow_DarkRitual",
		["Dark Sludge"] = "spell_shadow_creepingplague",
		["Darkness"] = "Spell_Shadow_Twilight",
		["Dash"] = "Ability_Druid_Dash",
		["Dazed"] = "Spell_Frost_Stun",
		["Deadly Poison II"] = "Ability_Rogue_DualWeild",
		["Deadly Poison III"] = "Ability_Rogue_DualWeild",
		["Deadly Poison IV"] = "Ability_Rogue_DualWeild",
		["Deadly Poison V"] = "Ability_Rogue_DualWeild",
		["Deadly Poison"] = "Ability_Rogue_DualWeild",
		["Death Coil"] = "Spell_Shadow_DeathCoil",
		["Death Wish"] = "Spell_Shadow_DeathPact",
		["Decayed Strength"] = "spell_holy_harmundeadaura",
		["Deep Sleep"] = "Spell_Nature_Sleep",
		["Deep Slumber"] = "Spell_Shadow_Cripple",
		["Deep Wound"] = "Ability_BackStab",
		["Deep Wounds"] = "Ability_BackStab",
		["Defense"] = "Ability_Racial_ShadowMeld",
		["Defensive Stance Passive"] = "Ability_Warrior_DefensiveStance",
		["Defensive Stance"] = "Ability_Warrior_DefensiveStance",
		["Defensive State 2"] = "Ability_Defend",
		["Defensive State"] = "Ability_Defend",
		["Defiance"] = "Ability_Warrior_InnerRage",
		["Deflection"] = "Ability_Parry",
		["Delusions of Jin'do"] = "Spell_Shadow_UnholyFrenzy",
		["Demon Armor"] = "Spell_Shadow_RagingScream",
		["Demon Skin"] = "Spell_Shadow_RagingScream",
		["Demonic Embrace"] = "Spell_Shadow_Metamorphosis",
		["Demonic Frenzy"] = "Spell_Shadow_Metamorphosis",
		["Demonic Sacrifice"] = "Spell_Shadow_PsychicScream",
		["Demoralizing Roar"] = "Ability_Druid_DemoralizingRoar",
		["Demoralizing Shout"] = "Ability_Warrior_WarCry",
		["Desperate Prayer"] = "Spell_Holy_Restoration",
		["Destructive Reach"] = "Spell_Shadow_CorpseExplode",
		["Detect Greater Invisibility"] = "Spell_Shadow_DetectInvisibility",
		["Detect Invisibility"] = "Spell_Shadow_DetectInvisibility",
		["Detect Lesser Invisibility"] = "Spell_Shadow_DetectLesserInvisibility",
		["Detect Magic"] = "Spell_Holy_Dizzy",
		["Detect Traps"] = "Ability_Spy",
		["Detect"] = "Ability_Hibernation",
		["Deterrence"] = "Ability_Whirlwind",
		["Detonation"] = "Spell_Fire_Fire",
		["Devastate"] = "INV_Sword_11",
		["Devastation"] = "Spell_Fire_FlameShock",
		["Devotion Aura"] = "Spell_Holy_DevotionAura",
		["Devour Magic Effect"] = "Spell_Nature_Purge",
		["Devour Magic"] = "Spell_Nature_Purge",
		["Devouring Plague"] = "Spell_Shadow_BlackPlague",
		["Diamond Flask"] = "INV_Drink_01",
		["Dimensional Ripper - Everlook"] = "Inv_misc_enggizmos_15",
		["Diplomacy"] = "INV_Misc_Note_02",
		["Dire Bear Form"] = "Ability_Racial_BearForm",
		["Dire Growl"] = "Ability_Racial_Cannibalize",
		["Disarm Trap"] = "Spell_Shadow_GrimWard",
		["Disarm"] = "Ability_Warrior_Disarm",
		["Disease Cleansing Totem"] = "Spell_Nature_DiseaseCleansingTotem",
		["Disease Cloud"] = "Spell_Nature_AbolishMagic",
		["Diseased Shot"] = "Spell_Shadow_CallofBone",
		["Diseased Slime"] = "spell_shadow_creepingplague",
		["Diseased Spit"] = "Spell_Shadow_CreepingPlague",
		["Disenchant"] = "Spell_Holy_RemoveCurse",
		["Disengage"] = "Ability_Rogue_Feint",
		["Disjunction"] = "Spell_Lightning_LightningBolt01",
		["Dismiss Pet"] = "Spell_Nature_SpiritWolf",
		["Dispel Magic"] = "Spell_Holy_DispelMagic",
		["Distract"] = "Ability_Rogue_Distract",
		["Distracting Pain"] = "Ability_Racial_Cannibalize",
		["Distracting Shot"] = "Spell_Arcane_Blink",
		["Dive"] = "Spell_Shadow_BurningSpirit",
		["Divine Favor"] = "Spell_Holy_Heal",
		["Divine Fury"] = "Spell_Holy_SealOfWrath",
		["Divine Intellect"] = "Spell_Nature_Sleep",
		["Divine Intervention"] = "Spell_Nature_TimeStop",
		["Divine Protection"] = "Spell_Holy_Restoration",
		["Divine Shield"] = "Spell_Holy_DivineIntervention",
		["Divine Spirit"] = "Spell_Holy_DivineSpirit",
		["Divine Strength"] = "Ability_GolemThunderClap",
		["Diving Sweep"] = "Ability_Warrior_Cleave",
		["Dodge"] = "Spell_Nature_Invisibilty",
		["Dominate Mind"] = "Spell_Shadow_ShadowWordDominate",
		["Drain Life"] = "Spell_Shadow_LifeDrain02",
		["Drain Mana"] = "Spell_Shadow_SiphonMana",
		["Drain Soul"] = "Spell_Shadow_Haunting",
		["Dredge Sickness"] = "Spell_Nature_NullifyDisease",
		["Drink Minor Potion"] = "Spell_holy_heal",
		["Druid's Slumber"] = "Spell_Nature_Sleep",
		["Dual Wield Specialization"] = "Ability_DualWield",
		["Dual Wield"] = "Ability_DualWield",
		["Duel"] = "Temp",
		["Dust Field"] = "Spell_Nature_Cyclone",
		["Dynamite"] = "spell_fire_selfdestruct",
		["Eagle Eye"] = "Ability_Hunter_EagleEye",
		["Earth Shock"] = "Spell_Nature_EarthShock",
		["Earthbind Totem"] = "Spell_Nature_StrengthOfEarthTotem02",
		["Earthborer Acid"] = "Spell_Nature_Acid_01",
		["Earthgrab Totem"] = "spell_nature_naturetouchdecay",
		["Earthgrab"] = "Spell_Nature_NatureTouchDecay",
		["Efficiency"] = "Spell_Frost_WizardMark",
		["Electric Discharge"] = "Spell_Lightning_LightningBolt01",
		["Electrified Net"] = "Ability_Ensnare",
		["Elemental Fire"] = "Spell_fire_fireball02",
		["Elemental Focus"] = "Spell_Shadow_ManaBurn",
		["Elemental Fury"] = "Spell_Fire_Volcano",
		["Elemental Mastery"] = "Spell_Nature_WispHeal",
		["Elemental Precision"] = "Spell_Ice_MagicDamage",
		["Elune's Grace"] = "Spell_Holy_ElunesGrace",
		["Elusiveness"] = "Spell_Magic_LesserInvisibilty",
		["Emberstorm"] = "Spell_Fire_SelfDestruct",
		["Enchanting"] = "Trade_Engraving",
		["Endurance Training"] = "Spell_Nature_Reincarnation",
		["Endurance"] = "Spell_Nature_UnyeildingStamina",
		["Engineering Specialization"] = "INV_Misc_Gear_01",
		["Engineering"] = "Trade_Engineering",
		["Enrage"] = "Ability_Druid_Enrage",
		["Enslave Demon"] = "Spell_Shadow_EnslaveDemon",
		["Entangling Roots"] = "Spell_Nature_StrangleVines",
		["Entrapment"] = "Spell_Nature_StrangleVines",
		["Enveloping Web"] = "Spell_Nature_EarthBind",
		["Enveloping Webs"] = "Spell_Nature_EarthBind",
		["Enveloping Winds"] = "Spell_Nature_Cyclone",
		["Ephemeral Power"] = "Spell_Holy_MindVision",
		["Escape Artist"] = "Ability_Rogue_Trip",
		["Essence of Sapphiron"] = "INV_Trinket_Naxxramas06",
		["Evasion"] = "Spell_Shadow_ShadowWard",
		["Eventide"] = "Spell_Frost_Stun",
		["Eviscerate"] = "Ability_Rogue_Eviscerate",
		["Evocation"] = "Spell_Nature_Purge",
		["Execute"] = "INV_Sword_48",
		["Exorcism"] = "Spell_Holy_Excorcism_02",
		["Expansive Mind"] = "INV_Enchant_EssenceEternalLarge",
		["Explode"] = "Spell_fire_selfdestruct",
		["Exploding Shot"] = "Spell_Fire_Fireball02",
		["Exploit Weakness"] = "Ability_BackStab",
		["Explosive Shot"] = "Spell_Fire_Fireball02",
		["Explosive Trap Effect"] = "Spell_Fire_SelfDestruct",
		["Explosive Trap"] = "Spell_Fire_SelfDestruct",
		["Expose Armor"] = "Ability_Warrior_Riposte",
		["Expose Weakness"] = "Ability_CriticalStrike",
		["Eye for an Eye"] = "Spell_Holy_EyeforanEye",
		["Eye of Kilrogg"] = "Spell_Shadow_EvilEye",
		["Eyes of the Beast"] = "Ability_EyeOfTheOwl",
		["Fade"] = "Spell_Magic_LesserInvisibilty",
		["Faerie Fire (Feral)"] = "Spell_Nature_FaerieFire",
		["Faerie Fire"] = "Spell_Nature_FaerieFire",
		["Far Sight"] = "Spell_Nature_FarSight",
		["Fatal Bite"] = "Ability_BackStab",
		["Fear Ward"] = "Spell_Holy_Excorcism",
		["Fear"] = "Spell_Shadow_Possession",
		["Feed Pet"] = "Ability_Hunter_BeastTraining",
		["Feedback"] = "Spell_Shadow_RitualOfSacrifice",
		["Feign Death"] = "Ability_Rogue_FeignDeath",
		["Feint"] = "Ability_Rogue_Feint",
		["Fel Concentration"] = "Spell_Shadow_FingerOfDeath",
		["Fel Domination"] = "Spell_Nature_RemoveCurse",
		["Fel Intellect"] = "Spell_Holy_MagicalSentry",
		["Fel Stamina"] = "Spell_Shadow_AntiShadow",
		["Fel Stomp"] = "Ability_WarStomp",
		["Felfire"] = "Spell_Fire_Fireball",
		["Feline Grace"] = "INV_Feather_01",
		["Feline Swiftness"] = "Spell_Nature_SpiritWolf",
		["Feral Aggression"] = "Ability_Druid_DemoralizingRoar",
		["Feral Charge"] = "Ability_Hunter_Pet_Bear",
		["Feral Charge Effect"] = "ability_hunter_pet_bear",
		["Feral Instinct"] = "Ability_Ambush",
		["Ferocious Bite"] = "Ability_Druid_FerociousBite",
		["Ferocity"] = "INV_Misc_MonsterClaw_04",
		["Fetish"] = "INV_Misc_Horn_01",
		["Fevered Fatigue"] = "spell_nature_nullifydisease",
		["Fevered Plague"] = "Spell_Nature_NullifyDisease",
		["Fiery Burst"] = "Spell_Fire_FireBolt",
		["Find Herbs"] = "INV_Misc_Flower_02",
		["Find Minerals"] = "Spell_Nature_Earthquake",
		["Find Treasure"] = "Racial_Dwarf_FindTreasure",
		["Fire Blast"] = "Spell_Fire_Fireball",
		["Fire Nova Totem"] = "Spell_Fire_SealOfFire",
		["Fire Nova"] = "Spell_Fire_SealOfFire",
		["Fire Power"] = "Spell_Fire_Immolation",
		["Fire Resistance Aura"] = "Spell_Fire_SealOfFire",
		["Fire Resistance Totem"] = "Spell_FireResistanceTotem_01",
		["Fire Resistance"] = "Spell_Fire_FireArmor",
		["Fire Shield Effect II"] = "Spell_Fire_Immolation",
		["Fire Shield Effect III"] = "Spell_Fire_Immolation",
		["Fire Shield Effect IV"] = "Spell_Fire_Immolation",
		["Fire Shield Effect"] = "Spell_Fire_Immolation",
		["Fire Shield II"] = "spell_fire_immolation",
		["Fire Shield"] = "Spell_Fire_FireArmor",
		["Fire Storm"] = "Spell_Fire_SelfDestruct",
		["Fire Vulnerability"] = "Spell_Fire_SoulBurn",
		["Fire Ward"] = "Spell_Fire_FireArmor",
		["Fire Weakness"] = "INV_Misc_QirajiCrystal_02",
		["Fireball Volley"] = "Spell_Fire_FlameBolt",
		["Fireball"] = "Spell_Fire_FlameBolt",
		["Firebolt"] = "Spell_Fire_FireBolt",
		["First Aid"] = "Spell_Holy_SealOfSacrifice",
		["Fishing Poles"] = "Trade_Fishing",
		["Fishing"] = "Trade_Fishing",
		["Fist of Ragnaros"] = "Spell_Holy_SealOfWrath",
		["Fist Weapon Specialization"] = "INV_Gauntlets_04",
		["Fist Weapons"] = "INV_Gauntlets_04",
		["Flame Buffet"] = "Spell_Fire_Fireball",
		["Flame Cannon"] = "Spell_Fire_FlameBolt",
		["Flame Lash"] = "Spell_Fire_Fireball",
		["Flame Shock"] = "Spell_Fire_FlameShock",
		["Flame Spike"] = "Spell_Fire_SelfDestruct",
		["Flame Spray"] = "Spell_Fire_Fire",
		["Flame Throwing"] = "Spell_Fire_Flare",
		["Flames of Shahram"] = "Spell_Fire_SelfDestruct",
		["Flamespit"] = "spell_fire_selfdestruct",
		["Flamestrike"] = "Spell_Fire_SelfDestruct",
		["Flamethrower"] = "Spell_Fire_Incinerate",
		["Flametongue Totem"] = "Spell_Nature_GuardianWard",
		["Flametongue Weapon"] = "Spell_Fire_FlameTounge",
		["Flare"] = "Spell_Fire_Flare",
		["Flash Bomb"] = "Spell_Shadow_DarkSummoning",
		["Flash Heal"] = "Spell_Holy_FlashHeal",
		["Flash of Light"] = "Spell_Holy_FlashHeal",
		["Flurry"] = "Ability_GhoulFrenzy",
		["Focused Casting"] = "Spell_Arcane_Blink",
		["Focused Mind"] = "Spell_Nature_MirrorImage",
		["Forbearance"] = "Spell_Holy_RemoveCurse",
		["Force of Nature"] = "Spell_Nature_ForceOfNature",
		["Force of Will"] = "Spell_Nature_SlowingTotem",
		["Force Punch"] = "INV_Gauntlets_31",
		["Force Reactive Disk"] = "Spell_Lightning_LightningBolt01",
		["Forked Lightning"] = "Spell_Nature_ChainLightning",
		["Forsaken Skills"] = "Spell_Shadow_AntiShadow",
		["Frailty"] = "Spell_Shadow_AnimateDead",
		["Freeze Solid"] = "Spell_Fost_Glacier",
		["Freezing Trap"] = "Spell_Frost_ChainsOfIce",
		["Freezing Trap Effect"] = "spell_frost_chainsofice",
		["Frenzied Regeneration"] = "Ability_BullRush",
		["Frenzy"] = "INV_Misc_MonsterClaw_03",
		["Frost Armor"] = "Spell_Frost_FrostArmor02",
		["Frost Breath"] = "Spell_Frost_FrostNova",
		["Frost Channeling"] = "Spell_Frost_Stun",
		["Frost Nova"] = "Spell_Frost_FrostNova",
		["Frost Resistance Aura"] = "Spell_Frost_WizardMark",
		["Frost Resistance Totem"] = "Spell_FrostResistanceTotem_01",
		["Frost Resistance"] = "Spell_Frost_FrostWard",
		["Frost Shock"] = "Spell_Frost_FrostShock",
		["Frost Shot"] = "Spell_Ice_MagicDamage",
		["Frost Trap Aura"] = "Spell_Frost_FreezingBreath",
		["Frost Trap"] = "Spell_Frost_FreezingBreath",
		["Frost Ward"] = "Spell_Frost_FrostWard",
		["Frost Warding"] = "Spell_Frost_FrostWard",
		["Frost Weakness"] = "INV_Misc_QirajiCrystal_04",
		["Frostbite"] = "Spell_Frost_FrostArmor",
		["Frostbolt Volley"] = "Spell_Frost_FrostBolt02",
		["Frostbolt"] = "Spell_Frost_FrostBolt02",
		["Frostbrand Weapon"] = "Spell_Frost_FrostBrand",
		["Furbolg Form"] = "Inv_misc_monsterclaw_04",
		["Furious Howl"] = "Ability_Hunter_Pet_Wolf",
		["Furor"] = "Spell_Holy_BlessingOfStamina",
		["Fury of Ragnaros"] = "Spell_holy_MindSooth",
		["Gahz'ranka Slam"] = "Ability_Devour",
		["Gahz'rilla Slam"] = "Ability_Devour",
		["Garrote"] = "Ability_Rogue_Garrote",
		["Gehennas' Curse"] = "Spell_Shadow_GatherShadows",
		["Generic"] = "INV_Shield_09",
		["Ghost Wolf"] = "Spell_Nature_SpiritWolf",
		["Ghostly Strike"] = "Spell_Shadow_Curse",
		["Gift of Life"] = "INV_Misc_Gem_Pearl_05",
		["Gift of Nature"] = "Spell_Nature_ProtectionformNature",
		["Gift of the Wild"] = "Spell_Nature_Regeneration",
		["Gnomish Death Ray"] = "inv_gizmo_08",
		["Goblin Dragon Gun"] = "Spell_Fire_Incinerate",
		["Goblin Sapper Charge"] = "Spell_Fire_SelfDestruct",
		["Gouge"] = "Ability_Gouge",
		["Grace of Air Totem"] = "Spell_Nature_InvisibilityTotem",
		["Grasping Vines"] = "Spell_Nature_StrangleVines",
		["Great Stamina"] = "Spell_Nature_UnyeildingStamina",
		["Greater Blessing of Kings"] = "Spell_Magic_GreaterBlessingofKings",
		["Greater Blessing of Light"] = "Spell_Holy_GreaterBlessingofLight",
		["Greater Blessing of Might"] = "Spell_Holy_GreaterBlessingofKings",
		["Greater Blessing of Salvation"] = "Spell_Holy_GreaterBlessingofSalvation",
		["Greater Blessing of Sanctuary"] = "Spell_Holy_GreaterBlessingofSanctuary",
		["Greater Blessing of Wisdom"] = "Spell_Holy_GreaterBlessingofWisdom",
		["Greater Heal"] = "Spell_Holy_GreaterHeal",
		["Grim Reach"] = "Spell_Shadow_CallofBone",
		["Ground Tremor"] = "Spell_Nature_Earthquake",
		["Grounding Totem"] = "Spell_Nature_GroundingTotem",
		["Grovel"] = "Temp",
		["Growl"] = "Ability_Physical_Taunt",
		["Guardian's Favor"] = "Spell_Holy_SealOfProtection",
		["Guillotine"] = "Ability_Warrior_PunishingBlow",
		["Gun Specialization"] = "INV_Musket_03",
		["Guns"] = "INV_Weapon_Rifle_01",
		["Hail Storm"] = "Spell_Frost_FrostBolt02",
		["Hammer of Justice"] = "Spell_Holy_SealOfMight",
		["Hammer of Wrath"] = "Ability_ThunderClap",
		["Hamstring"] = "Ability_ShockWave",
		["Harass"] = "Ability_Hunter_Harass",
		["Hardiness"] = "INV_Helmet_23",
		["Haunting Spirits"] = "Spell_Shadow_BlackPlague",
		["Hawk Eye"] = "Ability_TownWatch",
		["Head Crack"] = "Ability_ThunderBolt",
		["Heal"] = "Spell_Holy_Heal",
		["Healing Circle"] = "Spell_Holy_PrayerOfHealing02",
		["Healing Focus"] = "Spell_Holy_HealingFocus",
		["Healing Light"] = "Spell_Holy_HolyBolt",
		["Healing of the Ages"] = "Spell_Nature_HealingWaveGreater",
		["Healing Stream Totem"] = "INV_Spear_04",
		["Healing Touch"] = "Spell_Nature_HealingTouch",
		["Healing Ward"] = "spell_holy_layonhands",
		["Healing Wave"] = "Spell_Nature_MagicImmunity",
		["Healing Way"] = "Spell_Nature_Healingway",
		["Health Funnel"] = "Spell_Shadow_LifeDrain",
		["Heart of the Wild"] = "Spell_Holy_BlessingOfAgility",
		["Hearthstone"] = "INV_Misc_Rune_01",
		["Hellfire Effect"] = "Spell_Fire_Incinerate",
		["Hellfire"] = "Spell_Fire_Incinerate",
		["Hemorrhage"] = "Spell_Shadow_LifeDrain",
		["Herbalism"] = "Spell_Nature_NatureTouchGrow",
		["Heroic Strike"] = "Ability_Rogue_Ambush",
		["Heroism"] = "Spell_Holy_Renew",
		["Hex of Jammal'an"] = "Spell_Shadow_AntiShadow",
		["Hex of Weakness"] = "Spell_Shadow_FingerOfDeath",
		["Hex"] = "Spell_Nature_Polymorph",
		["Hibernate"] = "Spell_Nature_Sleep",
		["Hi-Explosive Bomb"] = "Inv_misc_bomb_07",
		["Holy Fire"] = "Spell_Holy_SearingLight",
		["Holy Light"] = "Spell_Holy_HolyBolt",
		["Holy Nova"] = "Spell_Holy_HolyNova",
		["Holy Power"] = "Spell_Holy_Power",
		["Holy Reach"] = "Spell_Holy_Purify",
		["Holy Shield"] = "Spell_Holy_BlessingOfProtection",
		["Holy Shock"] = "Spell_Holy_SearingLight",
		["Holy Smite"] = "Spell_Holy_HolySmite",
		["Holy Specialization"] = "Spell_Holy_SealOfSalvation",
		["Holy Strength"] = "Spell_Holy_BlessingOfStrength",
		["Holy Strike"] = "Ability_ThunderBolt",
		["Holy Wrath"] = "Spell_Holy_Excorcism",
		["Honorless Target"] = "Spell_Magic_LesserInvisibilty",
		["Hooked Net"] = "Ability_Ensnare",
		["Horse Riding"] = "Spell_Nature_Swiftness",
		["Howl of Terror"] = "Spell_Shadow_DeathScream",
		["Humanoid Slaying"] = "Spell_Holy_PrayerOfHealing",
		["Hunter's Mark"] = "Ability_Hunter_SniperShot",
		["Hurricane"] = "Spell_Nature_Cyclone",
		["Ice Armor"] = "Spell_Frost_FrostArmor02",
		["Ice Barrier"] = "Spell_Ice_Lament",
		["Ice Blast"] = "Spell_Frost_FrostNova",
		["Ice Block"] = "Spell_Frost_Frost",
		["Ice Nova"] = "Spell_Frost_FrostNova",
		["Ice Shards"] = "Spell_Frost_IceShard",
		["Icicle"] = "Spell_Frost_FrostBolt02",
		["Ignite"] = "Spell_Fire_Incinerate",
		["Illumination"] = "Spell_Holy_GreaterHeal",
		["Immolate"] = "Spell_Fire_Immolation",
		["Immolation Trap Effect"] = "Spell_Fire_FlameShock",
		["Immolation Trap"] = "Spell_Fire_FlameShock",
		["Impact"] = "Spell_Fire_MeteorStorm",
		["Impale"] = "Ability_Gouge",
		["Improved Ambush"] = "Ability_Rogue_Ambush",
		["Improved Arcane Explosion"] = "Spell_Nature_WispSplode",
		["Improved Arcane Missiles"] = "Spell_Nature_StarFall",
		["Improved Arcane Shot"] = "Ability_ImpalingBolt",
		["Improved Aspect of the Hawk"] = "Spell_Nature_RavenForm",
		["Improved Aspect of the Monkey"] = "Ability_Hunter_AspectOfTheMonkey",
		["Improved Backstab"] = "Ability_BackStab",
		["Improved Battle Shout"] = "Ability_Warrior_BattleShout",
		["Improved Berserker Rage"] = "Spell_Nature_AncestralGuardian",
		["Improved Blessing of Might"] = "Spell_Holy_FistOfJustice",
		["Improved Blessing of Wisdom"] = "Spell_Holy_SealOfWisdom",
		["Improved Blizzard"] = "Spell_Frost_IceStorm",
		["Improved Bloodrage"] = "Ability_Racial_BloodRage",
		["Improved Chain Heal"] = "Spell_Nature_HealingWaveGreater",
		["Improved Chain Lightning"] = "Spell_Nature_ChainLightning",
		["Improved Challenging Shout"] = "Ability_Warrior_Challange",
		["Improved Charge"] = "Ability_Warrior_Charge",
		["Improved Cheap Shot"] = "Ability_CheapShot",
		["Improved Cleave"] = "Ability_Warrior_Cleave",
		["Improved Concentration Aura"] = "Spell_Holy_MindSooth",
		["Improved Concussive Shot"] = "Spell_Frost_Stun",
		["Improved Cone of Cold"] = "Spell_Frost_Glacier",
		["Improved Corruption"] = "Spell_Shadow_AbominationExplosion",
		["Improved Counterspell"] = "Spell_Frost_IceShock",
		["Improved Curse of Agony"] = "Spell_Shadow_CurseOfSargeras",
		["Improved Curse of Exhaustion"] = "Spell_Shadow_GrimWard",
		["Improved Curse of Weakness"] = "Spell_Shadow_CurseOfMannoroth",
		["Improved Dampen Magic"] = "Spell_Nature_AbolishMagic",
		["Improved Deadly Poison"] = "Ability_Rogue_DualWeild",
		["Improved Demoralizing Shout"] = "Ability_Warrior_WarCry",
		["Improved Devotion Aura"] = "Spell_Holy_DevotionAura",
		["Improved Disarm"] = "Ability_Warrior_Disarm",
		["Improved Distract"] = "Ability_Rogue_Distract",
		["Improved Drain Life"] = "Spell_Shadow_LifeDrain02",
		["Improved Drain Mana"] = "Spell_Shadow_SiphonMana",
		["Improved Drain Soul"] = "Spell_Shadow_Haunting",
		["Improved Enrage"] = "Ability_Druid_Enrage",
		["Improved Enslave Demon"] = "Spell_Shadow_EnslaveDemon",
		["Improved Entangling Roots"] = "Spell_Nature_StrangleVines",
		["Improved Evasion"] = "Spell_Shadow_ShadowWard",
		["Improved Eviscerate"] = "Ability_Rogue_Eviscerate",
		["Improved Execute"] = "INV_Sword_48",
		["Improved Expose Armor"] = "Ability_Warrior_Riposte",
		["Improved Eyes of the Beast"] = "Ability_EyeOfTheOwl",
		["Improved Fade"] = "Spell_Magic_LesserInvisibilty",
		["Improved Feign Death"] = "Ability_Rogue_FeignDeath",
		["Improved Fire Blast"] = "Spell_Fire_Fireball",
		["Improved Fire Nova Totem"] = "Spell_Fire_SealOfFire",
		["Improved Fire Ward"] = "Spell_Fire_FireArmor",
		["Improved Fireball"] = "Spell_Fire_FlameBolt",
		["Improved Firebolt"] = "Spell_Fire_FireBolt",
		["Improved Firestone"] = "INV_Ammo_FireTar",
		["Improved Flamestrike"] = "Spell_Fire_SelfDestruct",
		["Improved Flametongue Weapon"] = "Spell_Fire_FlameTounge",
		["Improved Flash of Light"] = "Spell_Holy_FlashHeal",
		["Improved Frost Nova"] = "Spell_Frost_FreezingBreath",
		["Improved Frost Ward"] = "Spell_Frost_FrostWard",
		["Improved Frostbolt"] = "Spell_Frost_FrostBolt02",
		["Improved Frostbrand Weapon"] = "Spell_Frost_FrostBrand",
		["Improved Garrote"] = "Ability_Rogue_Garrote",
		["Improved Ghost Wolf"] = "Spell_Nature_SpiritWolf",
		["Improved Gouge"] = "Ability_Gouge",
		["Improved Grace of Air Totem"] = "Spell_Nature_InvisibilityTotem",
		["Improved Grounding Totem"] = "Spell_Nature_GroundingTotem",
		["Improved Hammer of Justice"] = "Spell_Holy_SealOfMight",
		["Improved Hamstring"] = "Ability_ShockWave",
		["Improved Healing Stream Totem"] = "INV_Spear_04",
		["Improved Healing Touch"] = "Spell_Nature_HealingTouch",
		["Improved Healing Wave"] = "Spell_Nature_MagicImmunity",
		["Improved Healing"] = "Spell_Holy_Heal02",
		["Improved Health Funnel"] = "Spell_Shadow_LifeDrain",
		["Improved Healthstone"] = "INV_Stone_04",
		["Improved Heroic Strike"] = "Ability_Rogue_Ambush",
		["Improved Hunter's Mark"] = "Ability_Hunter_SniperShot",
		["Improved Immolate"] = "Spell_Fire_Immolation",
		["Improved Imp"] = "Spell_Shadow_SummonImp",
		["Improved Inner Fire"] = "Spell_Holy_InnerFire",
		["Improved Instant Poison"] = "Ability_Poisons",
		["Improved Intercept"] = "Ability_Rogue_Sprint",
		["Improved Intimidating Shout"] = "Ability_GolemThunderClap",
		["Improved Judgement"] = "Spell_Holy_RighteousFury",
		["Improved Kick"] = "Ability_Kick",
		["Improved Kidney Shot"] = "Ability_Rogue_KidneyShot",
		["Improved Lash of Pain"] = "Spell_Shadow_Curse",
		["Improved Lay on Hands"] = "Spell_Holy_LayOnHands",
		["Improved Lesser Healing Wave"] = "Spell_Nature_HealingWaveLesser",
		["Improved Life Tap"] = "Spell_Shadow_BurningSpirit",
		["Improved Lightning Bolt"] = "Spell_Nature_Lightning",
		["Improved Lightning Shield"] = "Spell_Nature_LightningShield",
		["Improved Magma Totem"] = "Spell_Fire_SelfDestruct",
		["Improved Mana Burn"] = "Spell_Shadow_ManaBurn",
		["Improved Mana Shield"] = "Spell_Shadow_DetectLesserInvisibility",
		["Improved Mana Spring Totem"] = "Spell_Nature_ManaRegenTotem",
		["Improved Mark of the Wild"] = "Spell_Nature_Regeneration",
		["Improved Mend Pet"] = "Ability_Hunter_MendPet",
		["Improved Mind Blast"] = "Spell_Shadow_UnholyFrenzy",
		["Improved Moonfire"] = "Spell_Nature_StarFall",
		["Improved Nature's Grasp"] = "Spell_Nature_NaturesWrath",
		["Improved Overpower"] = "INV_Sword_05",
		["Improved Power Word: Fortitude"] = "Spell_Holy_WordFortitude",
		["Improved Power Word: Shield"] = "Spell_Holy_PowerWordShield",
		["Improved Prayer of Healing"] = "Spell_Holy_PrayerOfHealing02",
		["Improved Psychic Scream"] = "Spell_Shadow_PsychicScream",
		["Improved Pummel"] = "INV_Gauntlets_04",
		["Improved Regrowth"] = "Spell_Nature_ResistNature",
		["Improved Reincarnation"] = "Spell_Nature_Reincarnation",
		["Improved Rejuvenation"] = "Spell_Nature_Rejuvenation",
		["Improved Rend"] = "Ability_Gouge",
		["Improved Renew"] = "Spell_Holy_Renew",
		["Improved Retribution Aura"] = "Spell_Holy_AuraOfLight",
		["Improved Revenge"] = "Ability_Warrior_Revenge",
		["Improved Revive Pet"] = "Ability_Hunter_BeastSoothe",
		["Improved Righteous Fury"] = "Spell_Holy_SealOfFury",
		["Improved Rockbiter Weapon"] = "Spell_Nature_RockBiter",
		["Improved Rupture"] = "Ability_Rogue_Rupture",
		["Improved Sap"] = "Ability_Sap",
		["Improved Scorch"] = "Spell_Fire_SoulBurn",
		["Improved Scorpid Sting"] = "Ability_Hunter_CriticalShot",
		["Improved Seal of Righteousness"] = "Ability_ThunderBolt",
		["Improved Seal of the Crusader"] = "Spell_Holy_HolySmite",
		["Improved Searing Pain"] = "Spell_Fire_SoulBurn",
		["Improved Searing Totem"] = "Spell_Fire_SearingTotem",
		["Improved Serpent Sting"] = "Ability_Hunter_Quickshot",
		["Improved Shadow Bolt"] = "Spell_Shadow_ShadowBolt",
		["Improved Shadow Word: Pain"] = "Spell_Shadow_ShadowWordPain",
		["Improved Shield Bash"] = "Ability_Warrior_ShieldBash",
		["Improved Shield Block"] = "Ability_Defend",
		["Improved Shield Wall"] = "Ability_Warrior_ShieldWall",
		["Improved Shred"] = "Spell_Shadow_VampiricAura",
		["Improved Sinister Strike"] = "Spell_Shadow_RitualOfSacrifice",
		["Improved Slam"] = "Ability_Warrior_DecisiveStrike",
		["Improved Slice and Dice"] = "Ability_Rogue_SliceDice",
		["Improved Spellstone"] = "INV_Misc_Gem_Sapphire_01",
		["Improved Sprint"] = "Ability_Rogue_Sprint",
		["Improved Starfire"] = "Spell_Arcane_StarFire",
		["Improved Stoneclaw Totem"] = "Spell_Nature_StoneClawTotem",
		["Improved Stoneskin Totem"] = "Spell_Nature_StoneSkinTotem",
		["Improved Strength of Earth Totem"] = "Spell_Nature_EarthBindTotem",
		["Improved Succubus"] = "Spell_Shadow_SummonSuccubus",
		["Improved Sunder Armor"] = "Ability_Warrior_Sunder",
		["Improved Taunt"] = "Spell_Nature_Reincarnation",
		["Improved Thorns"] = "Spell_Nature_Thorns",
		["Improved Thunder Clap"] = "Ability_ThunderClap",
		["Improved Tranquility"] = "Spell_Nature_Tranquility",
		["Improved Vampiric Embrace"] = "Spell_Shadow_ImprovedVampiricEmbrace",
		["Improved Vanish"] = "Ability_Vanish",
		["Improved Voidwalker"] = "Spell_Shadow_SummonVoidWalker",
		["Improved Windfury Weapon"] = "Spell_Nature_Cyclone",
		["Improved Wing Clip"] = "Ability_Rogue_Trip",
		["Improved Wrath"] = "Spell_Nature_AbolishMagic",
		["Incinerate"] = "Spell_Fire_FlameShock",
		["Infected Bite"] = "Spell_Shadow_CallofBone",
		["Infected Wound"] = "Spell_Nature_NullifyDisease",
		["Inferno Effect"] = "spell_frost_stun",
		["Inferno Shell"] = "Spell_Fire_SelfDestruct",
		["Inferno"] = "Spell_Shadow_SummonInfernal",
		["Initiative"] = "Spell_Shadow_Fumble",
		["Ink Spray"] = "spell_nature_sleep",
		["Inner Fire"] = "Spell_Holy_InnerFire",
		["Inner Focus"] = "Spell_Frost_WindWalkOn",
		["Innervate"] = "Spell_Nature_Lightning",
		["Insect Swarm"] = "Spell_Nature_InsectSwarm",
		["Inspiration"] = "Spell_Holy_LayOnHands",
		["Instant Poison II"] = "Ability_Poisons",
		["Instant Poison III"] = "Ability_Poisons",
		["Instant Poison IV"] = "Ability_Poisons",
		["Instant Poison V"] = "Ability_Poisons",
		["Instant Poison VI"] = "Ability_Poisons",
		["Instant Poison"] = "Ability_Poisons",
		["Intensity"] = "Spell_Fire_LavaSpawn",
		["Intercept Stun"] = "Spell_Frost_Stun",
		["Intercept"] = "Ability_Rogue_Sprint",
		["Intimidating Roar"] = "Ability_GolemThunderClap",
		["Intimidating Shout"] = "Ability_GolemThunderClap",
		["Intimidation"] = "Ability_Devour",
		["Intoxicating Venom"] = "Ability_Creature_Poison_01",
		["Iron Grenade"] = "Inv_misc_bomb_08",
		["Iron Will"] = "Spell_Magic_MageArmor", -- INCORRECT! Needs path updated!!! Preferably before expansion.
		["Judgement of Command"] = "Ability_Warrior_InnerRage",
		["Judgement of Justice"] = "Spell_Holy_SealOfWrath",
		["Judgement of Light"] = "Spell_Holy_HealingAura",
		["Judgement of Righteousness"] = "Ability_ThunderBolt",
		["Judgement of the Crusader"] = "Spell_Holy_HolySmite",
		["Judgement of Wisdom"] = "Spell_Holy_RighteousnessAura",
		["Judgement"] = "Spell_Holy_RighteousFury",
		["Kick - Silenced"] = "Ability_Kick",
		["Kick"] = "Ability_Kick",
		["Kidney Shot"] = "Ability_Rogue_KidneyShot",
		["Killer Instinct"] = "Spell_Holy_BlessingOfStamina",
		["Knock Away"] = "INV_Gauntlets_05",
		["Knockdown"] = "Ability_GolemThunderClap",
		["Kodo Riding"] = "Spell_Nature_Swiftness",
		["Lacerate"] = "Spell_Shadow_VampiricAura",
		["Large Copper Bomb"] = "Inv_misc_bomb_01",
		["Larva Goo"] = "Ability_Creature_Poison_02",
		["Lash of Pain"] = "Spell_Shadow_Curse",
		["Lash"] = "Ability_CriticalStrike",
		["Last Stand"] = "Spell_Holy_AshesToAshes",
		["Lasting Judgement"] = "Spell_Holy_HealingAura",
		["Lava Spout Totem"] = "Spell_Fire_SelfDestruct",
		["Lay on Hands"] = "Spell_Holy_LayOnHands",
		["Leader of the Pack"] = "Spell_Nature_UnyeildingStamina",
		["Leather"] = "INV_Chest_Leather_09",
		["Leatherworking"] = "INV_Misc_ArmorKit_17",
		["Leech Poison"] = "Spell_Nature_NullifyPoison",
		["Lesser Heal"] = "Spell_Holy_LesserHeal",
		["Lesser Healing Wave"] = "Spell_Nature_HealingWaveLesser",
		["Lesser Invisibility"] = "Spell_Magic_LesserInvisibilty",
		["Lethal Shots"] = "Ability_SearingArrow",
		["Lethality"] = "Ability_CriticalStrike",
		["Levitate"] = "Spell_Holy_LayOnHands",
		["Libram"] = "INV_Misc_Book_11",
		["Lich Slap"] = "Spell_Shadow_ChillTouch",
		["Life Tap"] = "Spell_Shadow_BurningSpirit",
		["Lifegiving Gem"] = "INV_Misc_Gem_Pearl_05",
		["Lightning Blast"] = "Spell_Nature_Lightning",
		["Lightning Bolt"] = "Spell_Nature_Lightning",
		["Lightning Breath"] = "Spell_Nature_Lightning",
		["Lightning Cloud"] = "Spell_Nature_CallStorm",
		["Lightning Mastery"] = "Spell_Lightning_LightningBolt01",
		["Lightning Reflexes"] = "Spell_Nature_Invisibilty",
		["Lightning Shield"] = "Spell_Nature_LightningShield",
		["Lightning Wave"] = "Spell_Nature_ChainLightning",
		["Lightwell Renew"] = "Spell_Holy_SummonLightwell",
		["Lightwell"] = "Spell_Holy_SummonLightwell",
		["Lizard Bolt"] = "Spell_Nature_Lightning",
		["Localized Toxin"] = "Spell_Nature_CorrosiveBreath",
		["Long Daze"] = "Spell_Frost_Stun",
		["Mace Specialization"] = "INV_Mace_01",
		["Mace Stun Effect"] = "Spell_Frost_Stun",
		["Machine Gun"] = "Ability_Marksmanship",
		["Mage Armor"] = "Spell_MageArmor",
		["Magic Attunement"] = "Spell_Nature_AbolishMagic",
		["Magic Dust"] = "Inv_misc_dust_02",
		["Magma Blast"] = "Spell_fire_fireblast",
		["Magma Splash"] = "Spell_Fire_Immolation",
		["Magma Totem"] = "Spell_Fire_SelfDestruct",
		["Mail"] = "INV_Chest_Chain_05",
		["Malice"] = "Ability_Racial_BloodRage",
		["Mana Burn"] = "Spell_Shadow_ManaBurn",
		["Mana Feed"] = "Spell_Shadow_DarkRitual",
		["Mana Shield"] = "Spell_Shadow_DetectLesserInvisibility",
		["Mana Spring Totem"] = "Spell_Nature_ManaRegenTotem",
		["Mana Tide Totem"] = "Spell_Frost_SummonWaterElemental",
		["Mangle"] = "Ability_Druid_Mangle.tga",
		["Mark of Arlokk"] = "Ability_Hunter_SniperShot",
		["Mark of the Wild"] = "Spell_Nature_Regeneration",
		["Martyrdom"] = "Spell_Nature_Tranquility",
		["Mass Dispel"] = "Spell_Shadow_Teleport",
		["Master Demonologist"] = "Spell_Shadow_ShadowPact",
		["Master of Deception"] = "Spell_Shadow_Charm",
		["Master of Elements"] = "Spell_Fire_MasterOfElements",
		["Master Summoner"] = "Spell_Shadow_ImpPhaseShift",
		["Maul"] = "Ability_Druid_Maul",
		["Mechanostrider Piloting"] = "Spell_Nature_Swiftness",
		["Meditation"] = "Spell_Nature_Sleep",
		["Megavolt"] = "Spell_Nature_ChainLightning",
		["Melee Specialization"] = "INV_Axe_02",
		["Melt Ore"] = "Spell_Fire_SelfDestruct",
		["Mend Pet"] = "Ability_Hunter_MendPet",
		["Mental Agility"] = "Ability_Hibernation",
		["Mental Strength"] = "Spell_Nature_EnchantArmor",
		["Mighty Blow"] = "INV_Gauntlets_05",
		["Mind Blast"] = "Spell_Shadow_UnholyFrenzy",
		["Mind Control"] = "Spell_Shadow_ShadowWordDominate",
		["Mind Flay"] = "Spell_Shadow_SiphonMana",
		["Mind Soothe"] = "Spell_Holy_MindSooth",
		["Mind Tremor"] = "Spell_Nature_Earthquake",
		["Mind Vision"] = "Spell_Holy_MindVision",
		["Mind-numbing Poison II"] = "Spell_Nature_NullifyDisease",
		["Mind-numbing Poison III"] = "Spell_Nature_NullifyDisease",
		["Mind-numbing Poison"] = "Spell_Nature_NullifyDisease",
		["Mining"] = "Spell_Fire_FlameBlades",
		["Mithril Frag Bomb"] = "Inv_misc_bomb_02",
		["Mocking Blow"] = "Ability_Warrior_PunishingBlow",
		["Molten Blast"] = "Spell_Fire_Fire",
		["Molten Metal"] = "Spell_Fire_Fireball",
		["Mongoose Bite"] = "Ability_Hunter_SwiftStrike",
		["Monster Slaying"] = "INV_Misc_Head_Dragon_Black",
		["Moonfire"] = "Spell_Nature_StarFall",
		["Moonfury"] = "Spell_Nature_MoonGlow",
		["Moonglow"] = "Spell_Nature_Sentinal",
		["Moonkin Aura"] = "Spell_Nature_MoonGlow",
		["Moonkin Form"] = "Spell_Nature_ForceOfNature",
		["Mortal Cleave"] = "Ability_Warrior_SavageBlow",
		["Mortal Shots"] = "Ability_PierceDamage",
		["Mortal Strike"] = "Ability_Warrior_SavageBlow",
		["Mortal Wound"] = "Ability_CriticalStrike",
		["Multi-Shot"] = "Ability_UpgradeMoonGlaive",
		["Murder"] = "Spell_Shadow_DeathScream",
		["Naralex's Nightmare"] = "Spell_Nature_Sleep",
		["Natural Armor"] = "Spell_Nature_SpiritArmor",
		["Natural Shapeshifter"] = "Spell_Nature_WispSplode",
		["Natural Weapons"] = "INV_Staff_01",
		["Nature Aligned"] = "Spell_Nature_SpiritArmor",
		["Nature Resistance Totem"] = "Spell_Nature_NatureResistanceTotem",
		["Nature Resistance"] = "Spell_Nature_ResistNature",
		["Nature Weakness"] = "INV_Misc_QirajiCrystal_03",
		["Nature's Focus"] = "Spell_Nature_HealingWaveGreater",
		["Nature's Grace"] = "Spell_Nature_NaturesBlessing",
		["Nature's Grasp"] = "Spell_Nature_NaturesWrath",
		["Nature's Reach"] = "Spell_Nature_NatureTouchGrow",
		["Nature's Swiftness"] = "Spell_Nature_RavenForm",
		["Necrotic Poison"] = "Ability_Creature_Poison_03",
		["Negative Charge"] = "Spell_ChargeNegative",
		["Net"] = "Ability_Ensnare",
		["Nightfall"] = "Spell_Shadow_Twilight",
		["Noxious Catalyst"] = "Spell_Holy_HarmUndeadAura",
		["Noxious Cloud"] = "Spell_Nature_AbolishMagic",
		["Omen of Clarity"] = "Spell_Nature_CrystalBall",
		["One-Handed Axes"] = "INV_Axe_01",
		["One-Handed Maces"] = "INV_Mace_01",
		["One-Handed Swords"] = "Ability_MeleeDamage",
		["One-Handed Weapon Specialization"] = "INV_Sword_20",
		["Opening - No Text"] = "Temp",
		["Opening"] = "Temp",
		["Opportunity"] = "Ability_Warrior_WarCry",
		["Overpower"] = "Ability_MeleeDamage",
		["Pacify"] = "Ability_Seal",
		["Paralyzing Poison"] = "Ability_PoisonSting",
		["Paranoia"] = "Spell_Shadow_AuraOfDarkness",
		["Parasitic Serpent"] = "INV_Misc_MonsterHead_03",
		["Parry"] = "Ability_Parry",
		["Pathfinding"] = "Ability_Mount_JungleTiger",
		["Perception"] = "Spell_Nature_Sleep",
		["Permafrost"] = "Spell_Frost_Wisp",
		["Pet Aggression"] = "Ability_Druid_Maul",
		["Pet Hardiness"] = "Ability_BullRush",
		["Pet Recovery"] = "Ability_Hibernation",
		["Pet Resistance"] = "Spell_Holy_BlessingOfAgility",
		["Petrify"] = "Ability_GolemThunderClap",
		["Phase Shift"] = "Spell_Shadow_ImpPhaseShift",
		["Pick Lock"] = "Spell_Nature_MoonKey",
		["Pick Pocket"] = "INV_Misc_Bag_11",
		["Pierce Armor"] = "Spell_Shadow_VampiricAura",
		["Piercing Howl"] = "Spell_Shadow_DeathScream",
		["Piercing Ice"] = "Spell_Frost_Frostbolt",
		["Piercing Shadow"] = "Spell_Shadow_ChillTouch",
		["Piercing Shot"] = "Ability_SearingArrow",
		["Plague Cloud"] = "Spell_Shadow_CallofBone",
		["Plague Mind"] = "spell_shadow_callofbone",
		["Plate Mail"] = "INV_Chest_Plate01",
		["Poison Bolt Volley"] = "Spell_Nature_CorrosiveBreath",
		["Poison Bolt"] = "Spell_Nature_CorrosiveBreath",
		["Poison Cleansing Totem"] = "Spell_Nature_PoisonCleansingTotem",
		["Poison Cloud"] = "Spell_Nature_NatureTouchDecay",
		["Poison Shock"] = "Spell_Nature_Acid_01",
		["Poison"] = "Spell_Nature_CorrosiveBreath",
		["Poisoned Harpoon"] = "Ability_Poisons",
		["Poisoned Shot"] = "Ability_Poisons",
		["Poisonous Blood"] = "Spell_Nature_Regenerate",
		["Poisons"] = "Trade_BrewPoison",
		["Polearm Specialization"] = "INV_Weapon_Halbard_01",
		["Polearms"] = "INV_Spear_06",
		["Polymorph"] = "Spell_Nature_Polymorph",
		["Polymorph: Pig"] = "Spell_Magic_PolymorphPig",
		["Polymorph: Turtle"] = "Ability_Hunter_Pet_Turtle",
		["Portal: Darnassus"] = "Spell_Arcane_PortalDarnassus",
		["Portal: Ironforge"] = "Spell_Arcane_PortalIronForge",
		["Portal: Orgrimmar"] = "Spell_Arcane_PortalOrgrimmar",
		["Portal: Stormwind"] = "Spell_Arcane_PortalStormWind",
		["Portal: Thunder Bluff"] = "Spell_Arcane_PortalThunderBluff",
		["Portal: Undercity"] = "Spell_Arcane_PortalUnderCity",
		["Positive Charge"] = "Spell_ChargePositive",
		["Pounce Bleed"] = "Ability_Druid_SupriseAttack",
		["Pounce"] = "Ability_Druid_SupriseAttack",
		["Power Infusion"] = "Spell_Holy_PowerInfusion",
		["Power Word: Fortitude"] = "Spell_Holy_WordFortitude",
		["Power Word: Shield"] = "Spell_Holy_PowerWordShield",
		["Prayer Beads Blessing"] = "INV_Jewelry_Necklace_11",
		["Prayer of Fortitude"] = "Spell_Holy_PrayerOfFortitude",
		["Prayer of Healing"] = "Spell_Holy_PrayerOfHealing02",
		["Prayer of Shadow Protection"] = "Spell_Holy_PrayerofShadowProtection",
		["Prayer of Spirit"] = "Spell_Holy_PrayerofSpirit",
		["Precision"] = "Ability_Marksmanship",
		["Predatory Strikes"] = "Ability_Hunter_Pet_Cat",
		["Premeditation"] = "Spell_Shadow_Possession",
		["Preparation"] = "Spell_Shadow_AntiShadow",
		["Presence of Mind"] = "Spell_Nature_EnchantArmor",
		["Primal Fury"] = "Ability_Racial_Cannibalize",
		["Prowl"] = "Ability_Druid_SupriseAttack",
		["Psychic Scream"] = "Spell_Shadow_PsychicScream",
		["Pummel"] = "INV_Gauntlets_04",
		["Puncture"] = "Ability_Gouge",
		["Purge"] = "Spell_Nature_Purge",
		["Purification"] = "Spell_Frost_WizardMark",
		["Purify"] = "Spell_Holy_Purify",
		["Pursuit of Justice"] = "Spell_Holy_PersuitofJustice",
		["Putrid Breath"] = "Spell_Holy_HarmUndeadAura",
		["Putrid Enzyme"] = "Spell_Nature_NullifyDisease",
		["Pyroblast"] = "Spell_Fire_Fireball02",
		["Pyroclasm"] = "Spell_Fire_Volcano",
		["Quick Flame Ward"] = "spell_fire_sealoffire",
		["Quick Shots"] = "Ability_Warrior_Innerrage",
		["Quickness"] = "Ability_Racial_ShadowMeld",
		["Radiation Bolt"] = "Spell_Shadow_CorpseExplode",
		["Radiation Cloud"] = "Spell_Shadow_CorpseExplode",
		["Radiation Poisoning"] = "Spell_Shadow_CorpseExplode",
		["Radiation"] = "Spell_Shadow_CorpseExplode",
		["Rain of Fire"] = "Spell_Shadow_RainOfFire",
		["Rake"] = "Ability_Druid_Disembowel",
		["Ram Riding"] = "Spell_Nature_Swiftness",
		["Rampage"] = "Spell_Nature_NaturesWrath",
		["Ranged Weapon Specialization"] = "INV_Weapon_Rifle_06",
		["Rapid Concealment"] = "Ability_Ambush",
		["Rapid Fire"] = "Ability_Hunter_RunningShot",
		["Raptor Riding"] = "Spell_Nature_Swiftness",
		["Raptor Strike"] = "Ability_MeleeDamage",
		["Ravage"] = "Ability_Druid_Ravage",
		["Ravenous Claw"] = "Ability_GhoulFrenzy",
		["Readiness"] = "Spell_Nature_Sleep",
		["Rebirth"] = "Spell_Nature_Reincarnation",
		["Rebuild"] = "Spell_Shadow_LifeDrain",
		["Recently Bandaged"] = "INV_Misc_Bandage_08",
		["Reckless Charge"] = "Spell_nature_astralrecal",
		["Recklessness"] = "Ability_CriticalStrike",
		["Reckoning"] = "Spell_Holy_BlessingOfStrength",
		["Recombobulate"] = "Spell_Magic_PolymorphChicken",
		["Redemption"] = "Spell_Holy_Resurrection",
		["Redoubt"] = "Ability_Defend",
		["Reflection"] = "Spell_Frost_WindWalkOn",
		["Regeneration"] = "Spell_Nature_Regenerate",
		["Regrowth"] = "Spell_Nature_ResistNature",
		["Reincarnation"] = "Spell_Nature_Reincarnation",
		["Rejuvenation"] = "Spell_Nature_Rejuvenation",
		["Relentless Strikes"] = "Ability_Warrior_DecisiveStrike",
		["Remorseless Attacks"] = "Ability_FiegnDead",
		["Remorseless"] = "Ability_Fiegndead",
		["Remove Curse"] = "Spell_Holy_RemoveCurse",
		["Remove Insignia"] = "Temp",
		["Remove Lesser Curse"] = "Spell_Nature_RemoveCurse",
		["Rend"] = "Ability_Gouge",
		["Renew"] = "Spell_Holy_Renew",
		["Repentance"] = "Spell_Holy_PrayerOfHealing",
		["Repulsive Gaze"] = "Ability_GolemThunderClap",
		["Restorative Totems"] = "Spell_Nature_ManaRegenTotem",
		["Resurrection"] = "Spell_Holy_Resurrection",
		["Retaliation"] = "Ability_Warrior_Challange",
		["Retribution Aura"] = "Spell_Holy_AuraOfLight",
		["Revenge Stun"] = "Ability_Warrior_Revenge",
		["Revenge"] = "Ability_Warrior_Revenge",
		["Reverberation"] = "Spell_Frost_FrostWard",
		["Revive Pet"] = "Ability_Hunter_BeastSoothe",
		["Rhahk'Zor Slam"] = "INV_Gauntlets_05",
		["Ribbon of Souls"] = "Spell_Nature_Lightning",
		["Righteous Fury"] = "Spell_Holy_SealOfFury",
		["Rip"] = "Ability_GhoulFrenzy",
		["Riposte"] = "Ability_Warrior_Challange",
		["Ritual of Doom Effect"] = "Spell_Arcane_PortalDarnassus",
		["Ritual of Doom"] = "Spell_Shadow_AntiMagicShell",
		["Ritual of Summoning"] = "Spell_Shadow_Twilight",
		["Rockbiter Weapon"] = "Spell_Nature_RockBiter",
		["Rogue Passive"] = "Ability_Stealth",
		["Rough Copper Bomb"] = "Inv_misc_bomb_09",
		["Ruin"] = "Spell_Shadow_ShadowWordPain",
		["Rupture"] = "Ability_Rogue_Rupture",
		["Ruthlessness"] = "Ability_Druid_Disembowel",
		["Sacrifice"] = "Spell_Shadow_SacrificialShield",
		["Safe Fall"] = "INV_Feather_01",
		["Sanctity Aura"] = "Spell_Holy_MindVision",
		["Sap"] = "Ability_Sap",
		["Savage Fury"] = "Ability_Druid_Ravage",
		["Savage Strikes"] = "Ability_Racial_BloodRage",
		["Scare Beast"] = "Ability_Druid_Cower",
		["Scatter Shot"] = "Ability_GolemStormBolt",
		["Scorch"] = "Spell_Fire_SoulBurn",
		["Scorpid Poison"] = "Ability_PoisonSting",
		["Scorpid Sting"] = "Ability_Hunter_CriticalShot",
		["Screams of the Past"] = "Spell_Shadow_ImpPhaseShift",
		["Screech"] = "Ability_Hunter_Pet_Bat",
		["Seal Fate"] = "Spell_Shadow_ChillTouch",
		["Seal of Command"] = "Ability_Warrior_InnerRage",
		["Seal of Justice"] = "Spell_Holy_SealOfWrath",
		["Seal of Light"] = "Spell_Holy_HealingAura",
		["Seal of Reckoning"] = "Spell_Holy_SealOfWrath",
		["Seal of Righteousness"] = "Ability_ThunderBolt",
		["Seal of the Crusader"] = "Spell_Holy_HolySmite",
		["Seal of Wisdom"] = "Spell_Holy_RighteousnessAura",
		["Searing Light"] = "Spell_Holy_SearingLightPriest",
		["Searing Pain"] = "Spell_Fire_SoulBurn",
		["Searing Totem"] = "Spell_Fire_SearingTotem",
		["Second Wind"] = "INV_Jewelry_Talisman_06",
		["Seduction"] = "Spell_Shadow_MindSteal",
		["Sense Demons"] = "Spell_Shadow_Metamorphosis",
		["Sense Undead"] = "Spell_Holy_SenseUndead",
		["Sentry Totem"] = "Spell_Nature_RemoveCurse",
		["Serpent Sting"] = "Ability_Hunter_Quickshot",
		["Setup"] = "Spell_Nature_MirrorImage",
		["Shackle Undead"] = "Spell_Nature_Slow",
		["Shadow Affinity"] = "Spell_Shadow_ShadowWard",
		["Shadow Bolt Volley"] = "Spell_Shadow_ShadowBolt",
		["Shadow Bolt"] = "Spell_Shadow_ShadowBolt",
		["Shadow Flame"] = "Spell_fire_incinerate",
		["Shadow Focus"] = "Spell_Shadow_BurningSpirit",
		["Shadow Mastery"] = "Spell_Shadow_ShadeTrueSight",
		["Shadow Protection"] = "Spell_Shadow_AntiShadow",
		["Shadow Reach"] = "Spell_Shadow_ChillTouch",
		["Shadow Resistance Aura"] = "Spell_Shadow_SealOfKings",
		["Shadow Resistance"] = "Spell_Shadow_AntiShadow",
		["Shadow Shock"] = "Spell_Shadow_ShadowBolt",
		["Shadow Trance"] = "Spell_Shadow_Twilight",
		["Shadow Vulnerability"] = "Spell_Shadow_ShadowBolt",
		["Shadow Ward"] = "Spell_Shadow_AntiShadow",
		["Shadow Weakness"] = "INV_Misc_QirajiCrystal_05",
		["Shadow Weaving"] = "Spell_Shadow_BlackPlague",
		["Shadow Word: Pain"] = "Spell_Shadow_ShadowWordPain",
		["Shadowburn"] = "Spell_Shadow_ScourgeBuild",
		["Shadowform"] = "Spell_Shadow_Shadowform",
		["Shadowguard"] = "Spell_Nature_LightningShield",
		["Shadowmeld Passive"] = "Ability_Ambush",
		["Shadowmeld"] = "Ability_Ambush",
		["Sharpened Claws"] = "INV_Misc_MonsterClaw_04",
		["Shatter"] = "Spell_Frost_FrostShock",
		["Shell Shield"] = "Ability_Hunter_Pet_Turtle",
		["Shield Bash - Silenced"] = "Ability_Warrior_ShieldBash",
		["Shield Bash"] = "Ability_Warrior_ShieldBash",
		["Shield Block"] = "Ability_Defend",
		["Shield Slam"] = "INV_Shield_05",
		["Shield Specialization"] = "INV_Shield_06",
		["Shield Wall"] = "Ability_Warrior_ShieldWall",
		["Shield"] = "INV_Shield_04",
		["Shock"] = "Spell_Nature_WispHeal",
		["Shoot Bow"] = "Ability_Marksmanship",
		["Shoot Crossbow"] = "Ability_Marksmanship",
		["Shoot Gun"] = "Ability_Marksmanship",
		["Shoot"] = "Ability_ShootWand",
		["Shred"] = "Spell_Shadow_VampiricAura",
		["Shrink"] = "Spell_Shadow_AntiShadow",
		["Silence"] = "Spell_Shadow_ImpPhaseShift",
		["Silencing Shot"] = "Spell_Holy_Silence",
		["Silent Resolve"] = "Spell_Nature_ManaRegenTotem",
		["Silithid Pox"] = "spell_nature_nullifydisease",
		["Sinister Strike"] = "Spell_Shadow_RitualOfSacrifice",
		["Siphon Life"] = "Spell_Shadow_Requiem",
		["Skinning"] = "INV_Misc_Pelt_Wolf_01",
		["Skull Crack"] = "Spell_Frost_Stun",
		["Slam"] = "Ability_Warrior_DecisiveStrike",
		["Sleep"] = "Spell_Nature_Sleep",
		["Slice and Dice"] = "Ability_Rogue_SliceDice",
		["Slow Fall"] = "Spell_Magic_FeatherFall",
		["Slow"] = "Spell_Nature_Slow",
		["Slowing Poison"] = "Spell_Nature_SlowPoison",
		["Small Bronze Bomb"] = "Inv_misc_bomb_09",
		["Smelting"] = "Spell_Fire_FlameBlades",
		["Smite Slam"] = "INV_Gauntlets_05",
		["Smite Stomp"] = "Ability_WarStomp",
		["Smite"] = "Spell_Holy_HolySmite",
		["Smoke Bomb"] = "Ability_Hibernation",
		["Snap Kick"] = "Ability_Kick",
		["Sonic Burst"] = "Spell_Shadow_Teleport",
		["Soothe Animal"] = "Ability_Hunter_BeastSoothe",
		["Soothing Kiss"] = "Spell_Shadow_SoothingKiss",
		["Soul Bite"] = "Spell_Shadow_SiphonMana",
		["Soul Drain"] = "Spell_Shadow_LifeDrain02",
		["Soul Fire"] = "Spell_Fire_Fireball02",
		["Soul Link"] = "Spell_Shadow_GatherShadows",
		["Soul Siphon"] = "Spell_Shadow_LifeDrain02",
		["Soul Tap"] = "Spell_Shadow_LifeDrain02",
		["Soulstone Resurrection"] = "INV_Misc_Orb_04",
		["Spell Lock"] = "Spell_Shadow_MindRot",
		["Spell Reflection"] = "Spell_Shadow_Teleport",
		["Spell Warding"] = "Spell_Holy_SpellWarding",
		["Spirit Bond"] = "Ability_Druid_DemoralizingRoar",
		["Spirit Burst"] = "Spell_Shadow_Teleport",
		["Spirit of Redemption"] = "INV_Enchant_EssenceEternalLarge",
		["Spirit Tap"] = "Spell_Shadow_Requiem",
		["Spiritual Focus"] = "Spell_Arcane_Blink",
		["Spiritual Guidance"] = "Spell_Holy_SpiritualGuidence",
		["Spiritual Healing"] = "Spell_Nature_MoonGlow",
		["Spit"] = "Spell_Nature_CorrosiveBreath",
		["Spore Cloud"] = "Spell_Nature_DryadDispelMagic",
		["Sprint"] = "Ability_Rogue_Sprint",
		["Starfire Stun"] = "Spell_Arcane_StarFire",
		["Starfire"] = "Spell_Arcane_StarFire",
		["Starshards"] = "Spell_Arcane_StarFire",
		["Staves"] = "INV_Staff_08",
		["Stealth"] = "Ability_Stealth",
		["Stoneclaw Totem"] = "Spell_Nature_StoneClawTotem",
		["Stoneform"] = "Spell_Shadow_UnholyStrength",
		["Stoneskin Totem"] = "Spell_Nature_StoneSkinTotem",
		["Stormstrike"] = "Spell_Holy_SealOfMight",
		["Strength of Earth Totem"] = "Spell_Nature_EarthBindTotem",
		["Strike"] = "Ability_Rogue_Ambush",
		["Stuck"] = "Spell_Shadow_Teleport",
		["Stun"] = "Spell_Shadow_Teleport",
		["Subtlety"] = "Ability_EyeOfTheOwl",
		["Suffering"] = "Spell_Shadow_BlackPlague",
		["Summon Charger"] = "Ability_Mount_Charger",
		["Summon Dreadsteed"] = "Ability_Mount_Dreadsteed",
		["Summon Felhunter"] = "Spell_Shadow_SummonFelHunter",
		["Summon Felsteed"] = "Spell_Nature_Swiftness",
		["Summon Imp"] = "Spell_Shadow_SummonImp",
		["Summon Ragnaros"] = "Spell_fire_lavaspawn",
		["Summon Spawn of Bael'Gar"] = "Spell_Fire_LavaSpawn",
		["Summon Succubus"] = "Spell_Shadow_SummonSuccubus",
		["Summon Voidwalker"] = "Spell_Shadow_SummonVoidWalker",
		["Summon Warhorse"] = "Spell_Nature_Swiftness",
		["Summon Water Elemental"] = "Spell_Shadow_Sealofkings",
		["Sunder Armor"] = "Ability_Warrior_Sunder",
		["Suppression"] = "Spell_Shadow_UnsummonBuilding",
		["Surefooted"] = "Ability_Kick",
		["Survivalist"] = "Spell_Shadow_Twilight",
		["Sweeping Slam"] = "Ability_Devour",
		["Sweeping Strikes"] = "Ability_Rogue_SliceDice",
		["Swiftmend"] = "INV_Relics_IdolofRejuvenation",
		["Swipe"] = "INV_Misc_MonsterClaw_03",
		["Swoop"] = "Ability_Warrior_Cleave",
		["Sword Specialization"] = "INV_Sword_27",
		["Tactical Mastery"] = "Spell_Nature_EnchantArmor",
		["Tailoring"] = "Trade_Tailoring",
		["Tainted Blood"] = "Spell_Shadow_LifeDrain",
		["Tame Beast"] = "Ability_Hunter_BeastTaming",
		["Tamed Pet Passive"] = "Ability_Mount_PinkTiger",
		["Taunt"] = "Spell_Nature_Reincarnation",
		["Teleport: Darnassus"] = "Spell_Arcane_TeleportDarnassus",
		["Teleport: Ironforge"] = "Spell_Arcane_TeleportIronForge",
		["Teleport: Moonglade"] = "Spell_Arcane_TeleportMoonglade",
		["Teleport: Orgrimmar"] = "Spell_Arcane_TeleportOrgrimmar",
		["Teleport: Stormwind"] = "Spell_Arcane_TeleportStormWind",
		["Teleport: Thunder Bluff"] = "Spell_Arcane_TeleportThunderBluff",
		["Teleport: Undercity"] = "Spell_Arcane_TeleportUnderCity",
		["Tendon Rip"] = "Ability_CriticalStrike",
		["Tendon Slice"] = "Ability_CriticalStrike",
		["Terrify"] = "Ability_Physical_Taunt",
		["Terrifying Screech"] = "Spell_Shadow_SummonImp",
		["The Eye of the Dead"] = "INV_Trinket_Naxxramas01",
		["The Furious Storm"] = "Spell_Nature_CallStorm",
		["The Human Spirit"] = "INV_Enchant_ShardBrilliantSmall",
		["Thick Hide"] = "INV_Misc_Pelt_Bear_03",
		["Thorium Grenade"] = "Inv_misc_bomb_08",
		["Thorn Volley"] = "Spell_Nature_NaturesWrath",
		["Thorns"] = "Spell_Nature_Thorns",
		["Thousand Blades"] = "INV_Sword_53",
		["Threatening Gaze"] = "Spell_Shadow_Charm",
		["Throw Axe"] = "INV_Axe_08",
		["Throw Dynamite"] = "Spell_Fire_SelfDestruct",
		["Throw Liquid Fire"] = "Spell_Fire_MeteorStorm",
		["Throw Wrench"] = "INV_Misc_Wrench_01",
		["Throw"] = "Ability_Throw",
		["Throwing Specialization"] = "INV_ThrowingAxe_03",
		["Throwing Weapon Specialization"] = "INV_ThrowingKnife_01",
		["Thrown"] = "INV_ThrowingKnife_02",
		["Thunder Clap"] = "Spell_Nature_ThunderClap",
		["Thunderclap"] = "Spell_Nature_ThunderClap",
		["Thunderfury"] = "Spell_Nature_Cyclone",
		["Thundering Strikes"] = "Ability_ThunderBolt",
		["Thundershock"] = "Spell_Lightning_LightningBolt01",
		["Thunderstomp"] = "Ability_Hunter_Pet_Gorilla",
		["Tidal Charm"] = "spell_frost_summonwaterelemental",
		["Tidal Focus"] = "Spell_Frost_ManaRecharge",
		["Tidal Mastery"] = "Spell_Nature_Tranquility",
		["Tiger Riding"] = "Spell_Nature_Swiftness",
		["Tiger's Fury"] = "Ability_Mount_JungleTiger",
		["Torment"] = "Spell_Shadow_GatherShadows",
		["Totem of Wrath"] = "Spell_Fire_TotemOfWrath",
		["Totem"] = "Spell_Nature_StoneClawTotem",
		["Totemic Focus"] = "Spell_Nature_MoonGlow",
		["Touch of Weakness"] = "Spell_Shadow_DeadofNight",
		["Toughness"] = "Spell_Holy_Devotion",
		["Toxic Saliva"] = "Spell_Nature_CorrosiveBreath",
		["Toxic Spit"] = "Spell_Nature_CorrosiveBreath",
		["Toxic Volley"] = "Spell_Nature_CorrosiveBreath",
		["Traces of Silithyst"] = "Spell_Nature_TimeStop",
		["Track Beasts"] = "Ability_Tracking",
		["Track Demons"] = "Spell_Shadow_SummonFelHunter",
		["Track Dragonkin"] = "INV_Misc_Head_Dragon_01",
		["Track Elementals"] = "Spell_Frost_SummonWaterElemental",
		["Track Giants"] = "Ability_Racial_Avatar",
		["Track Hidden"] = "Ability_Stealth",
		["Track Humanoids"] = "Spell_Holy_PrayerOfHealing",
		["Track Undead"] = "Spell_Shadow_DarkSummoning",
		["Trample"] = "Spell_Nature_NaturesWrath",
		["Tranquil Air Totem"] = "Spell_Nature_Brilliance",
		["Tranquil Spirit"] = "Spell_Holy_ElunesGrace",
		["Tranquility"] = "Spell_Nature_Tranquility",
		["Tranquilizing Poison"] = "Ability_Creature_Poison_03",
		["Tranquilizing Shot"] = "Spell_Nature_Drowsy",
		["Trap Mastery"] = "Ability_Ensnare",
		["Travel Form"] = "Ability_Druid_TravelForm",
		["Trelane's Freezing Touch"] = "spell_shadow_unsummonbuilding",
		["Tremor Totem"] = "Spell_Nature_TremorTotem",
		["Trueshot Aura"] = "Ability_TrueShot",
		["Turn Undead"] = "Spell_Holy_TurnUndead",
		["Twisted Tranquility"] = "Spell_Nature_Tranquility",
		["Two-Handed Axes and Maces"] = "INV_Axe_10",
		["Two-Handed Axes"] = "INV_Axe_04",
		["Two-Handed Maces"] = "INV_Mace_04",
		["Two-Handed Swords"] = "Ability_MeleeDamage",
		["Two-Handed Weapon Specialization"] = "INV_Axe_09",
		["Ultrasafe Transporter: Gadgetzan"] = "Inv_misc_enggizmos_12",
		["Unarmed"] = "Ability_GolemThunderClap",
		["Unbreakable Will"] = "Spell_Magic_MageArmor",
		["Unbridled Wrath Effect"] = "Spell_Nature_StoneClawTotem",
		["Unbridled Wrath"] = "Spell_Nature_StoneClawTotem",
		["Undead Horsemanship"] = "Spell_Nature_Swiftness",
		["Underwater Breathing"] = "Spell_Shadow_DemonBreath",
		["Unending Breath"] = "Spell_Shadow_DemonBreath",
		["Unholy Frenzy"] = "Spell_Nature_BloodLust",
		["Unholy Power"] = "Spell_Shadow_ShadowWordDominate",
		["Unleashed Fury"] = "Ability_BullRush",
		["Unleashed Rage"] = "Spell_Nature_Ancestralguardian",
		["Unstable Concoction"] = "Spell_Fire_Incinerate",
		["Unstable Power"] = "Trade_Engineering",
		["Unyielding Faith"] = "Spell_Holy_UnyieldingFaith",
		["Uppercut"] = "INV_Gauntlets_05",
		["Vampiric Embrace"] = "Spell_Shadow_UnsummonBuilding",
		["Vanish"] = "Ability_Vanish",
		["Vanished"] = "Ability_Vanish",
		["Veil of Shadow"] = "Spell_Shadow_GatherShadows",
		["Vengeance"] = "Spell_Nature_Purge",
		["Venom Spit"] = "Spell_Nature_CorrosiveBreath",
		["Venom Sting"] = "Ability_PoisonSting",
		["Venomhide Poison"] = "Ability_Rogue_DualWeild",
		["Vicious Rend"] = "Ability_Gouge",
		["Vigor"] = "Spell_Nature_EarthBindTotem",
		["Vile Poisons"] = "Ability_Rogue_FeignDeath",
		["Vindication"] = "Spell_Holy_Vindication",
		["Viper Sting"] = "Ability_Hunter_AimedShot",
		["Virulent Poison"] = "Spell_Nature_CorrosiveBreath",
		["Void Bolt"] = "Spell_Shadow_ShadowBolt",
		["Volley"] = "Ability_Marksmanship",
		["Walking Bomb Effect"] = "Spell_Fire_SelfDestruct",
		["Wand Specialization"] = "INV_Wand_01",
		["Wandering Plague"] = "Spell_Shadow_CorpseExplode",
		["Wands"] = "Ability_ShootWand",
		["War Stomp"] = "Ability_WarStomp",
		["Water Breathing"] = "Spell_Shadow_DemonBreath",
		["Water Walking"] = "Spell_Frost_WindWalkOn",
		["Water"] = "Spell_Frost_SummonWaterElemental",
		["Waterbolt"] = "Spell_Frost_FrostBolt",
		["Wavering Will"] = "Spell_Shadow_AnimateDead",
		["Weak Frostbolt"] = "spell_frost_frostbolt02",
		["Weakened Soul"] = "Spell_Holy_AshesToAshes",
		["Web Explosion"] = "Ability_Ensnare",
		["Web Spin"] = "Spell_Nature_EarthBind",
		["Web Spray"] = "Ability_Ensnare",
		["Web"] = "Ability_Ensnare",
		["Whirling Barrage"] = "INV_Spear_05",
		["Whirling Trip"] = "INV_Spear_05",
		["Whirlwind"] = "Ability_Whirlwind",
		["Wide Slash"] = "Ability_Warrior_Cleave",
		["Will of Hakkar"] = "Spell_Shadow_ShadowWordDominate",
		["Will of the Forsaken"] = "Spell_Shadow_RaiseDead",
		["Windfury Totem"] = "Spell_Nature_Windfury",
		["Windfury Weapon"] = "Spell_Nature_Cyclone",
		["Windsor's Frenzy"] = "Ability_Racial_BloodRage",
		["Windwall Totem"] = "Spell_Nature_EarthBind",
		["Wing Buffet"] = "Inv_misc_monsterscales_14",
		["Wing Clip"] = "Ability_Rogue_Trip",
		["Wing Flap"] = "Spell_Nature_EarthBind",
		["Winter's Chill"] = "Spell_Frost_ChillingBlast",
		["Wisp Spirit"] = "Spell_Nature_WispSplode",
		["Wither Touch"] = "spell_nature_drowsy",
		["Wolf Riding"] = "Spell_Nature_Swiftness",
		["Wound Poison II"] = "INV_Misc_Herb_16",
		["Wound Poison III"] = "INV_Misc_Herb_16",
		["Wound Poison IV"] = "INV_Misc_Herb_16",
		["Wound Poison"] = "INV_Misc_Herb_16",
		["Wrath of Air Totem"] = "Spell_Nature_SlowingTotem",
		["Wrath"] = "Spell_Nature_AbolishMagic",
		["Wyvern Sting"] = "INV_Spear_02",
	}

	-- #FORCE_DOC
	--[[---------------------------------------------------------------------------
Arguments:
	string - name of the spell in either English or the current locale.
Returns:
	string or nil - the full path to the icon or nil if it doesn't exist
Example:
	local BS = LibStub("LibBabble-Spell-3.0")
	assert(BS:GetSpellIcon("Wyvern Sting") == "Interface\\Icons\\INV_Spear_02")
-----------------------------------------------------------------------------]]
	function lib:GetSpellIcon(spell)
		if type(spell) ~= "string" then
			error(format("Bad argument #2 to `GetSpellIcon'. Expected %q, got %q.", "string", type(spell)), 2)
		end
		local icon = spellIcons[spell] or spellIcons[self:GetReverseLookupTable()[spell]]
		if not icon then
			return nil
		end
		return "Interface\\Icons\\" .. icon
	end

	-- #FORCE_DOC
	--[[---------------------------------------------------------------------------
Arguments:
	string - name of the spell in either English or the current locale.
Returns:
	string or nil - the name of the icon or nil if it doesn't exist
Example:
	local BS = LibStub("LibBabble-Spell-3.0")
	assert(BS:GetSpellIcon("Wyvern Sting") == "INV_Spear_02")
-----------------------------------------------------------------------------]]
	function lib:GetShortSpellIcon(spell)
		if type(spell) ~= "string" then
			error(format("Bad argument #2 to `GetShortSpellIcon'. Expected %q, got %q.", "string", type(spell)), 2)
		end
		return spellIcons[spell] or spellIcons[self:GetReverseLookupTable()[spell]] or nil
	end

end)

if GetLocale() == "enUS" then
	table.insert(_G.LibBabble_Spell_3_0_funcs, function(lib)
		_G.LibBabble_Spell_3_0_foundLocale = true
		lib:SetCurrentTranslations(true)
	end)
end

table.insert(_G.LibBabble_Spell_3_0_funcs, function(lib)
	table.insert(_G.LibBabble_Spell_3_0_funcs, function(lib)
		if not _G.LibBabble_Spell_3_0_foundLocale then
			error(format("%s: Locale %q not supported", MAJOR_VERSION, GetLocale()))
		end
		_G.LibBabble_Spell_3_0_foundLocale = nil
	end)
end)
